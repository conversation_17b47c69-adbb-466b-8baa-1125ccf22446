#!/usr/bin/env python3
"""
Lighter价格更新修复测试脚本

测试修复后的Lighter客户端是否能够：
1. 正确检测连接问题
2. 及时重连
3. 恢复价格更新
"""

import asyncio
import time
import logging
from datetime import datetime
from src.exchanges.lighter_client import LighterClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LighterFixTester:
    """Lighter修复测试器"""
    
    def __init__(self):
        self.client = None
        self.price_updates = []
        self.connection_events = []
        self.test_start_time = None
        
    def on_orderbook_update(self, orderbook):
        """订单簿更新回调"""
        current_time = time.time()
        self.price_updates.append({
            'timestamp': current_time,
            'datetime': datetime.fromtimestamp(current_time).strftime('%H:%M:%S'),
            'symbol': orderbook.symbol,
            'bid': orderbook.bids[0][0] if orderbook.bids else None,
            'ask': orderbook.asks[0][0] if orderbook.asks else None,
            'mid_price': (orderbook.bids[0][0] + orderbook.asks[0][0]) / 2 if orderbook.bids and orderbook.asks else None
        })
        
        # 只显示最近的更新
        if len(self.price_updates) % 10 == 0:  # 每10次更新显示一次
            logger.info(f"📊 价格更新 #{len(self.price_updates)}: "
                       f"买价={orderbook.bids[0][0]:.2f}, "
                       f"卖价={orderbook.asks[0][0]:.2f}, "
                       f"中间价={((orderbook.bids[0][0] + orderbook.asks[0][0]) / 2):.2f}")
    
    async def test_connection_stability(self, duration_minutes=10):
        """测试连接稳定性"""
        logger.info(f"🧪 开始Lighter连接稳定性测试 (持续{duration_minutes}分钟)")
        
        self.test_start_time = time.time()
        
        # 初始化客户端
        self.client = LighterClient(symbol="BTC/USDT", is_paper_trading=True)
        self.client.set_orderbook_callback(self.on_orderbook_update)
        
        # 初始化连接
        success = await self.client.initialize()
        if not success:
            logger.error("❌ 客户端初始化失败")
            return False
        
        logger.info("✅ 客户端初始化成功，开始监控...")
        
        # 监控指定时间
        end_time = time.time() + (duration_minutes * 60)
        last_report_time = time.time()
        
        while time.time() < end_time:
            await asyncio.sleep(30)  # 每30秒检查一次
            
            current_time = time.time()
            
            # 每分钟报告一次状态
            if current_time - last_report_time >= 60:
                await self._report_status()
                last_report_time = current_time
        
        # 测试结束，生成报告
        await self._generate_final_report()
        
        # 清理
        await self.client.close()
        return True
    
    async def _report_status(self):
        """报告当前状态"""
        if not self.client:
            return
        
        current_time = time.time()
        elapsed_minutes = (current_time - self.test_start_time) / 60
        
        # 获取客户端状态
        status = self.client.status
        
        # 计算统计信息
        total_updates = len(self.price_updates)
        updates_per_minute = total_updates / elapsed_minutes if elapsed_minutes > 0 else 0
        
        # 检查最近的更新
        recent_updates = [u for u in self.price_updates if current_time - u['timestamp'] <= 60]
        recent_count = len(recent_updates)
        
        logger.info(f"📈 状态报告 (运行{elapsed_minutes:.1f}分钟):")
        logger.info(f"   总更新次数: {total_updates}")
        logger.info(f"   平均更新频率: {updates_per_minute:.1f}/分钟")
        logger.info(f"   最近1分钟更新: {recent_count}次")
        logger.info(f"   连接状态: {status.get('connection_status', 'unknown')}")
        logger.info(f"   连接健康: {status.get('connection_health', 'unknown')}")
        logger.info(f"   连接质量: {status.get('connection_quality', 0)}/100")
        logger.info(f"   重连次数: {status.get('reconnect_attempts', 0)}")
        logger.info(f"   连续失败: {status.get('consecutive_failures', 0)}")
        
        # 检查是否有问题
        if recent_count == 0:
            logger.warning("⚠️ 最近1分钟没有价格更新！")
        elif recent_count < 5:
            logger.warning(f"⚠️ 最近1分钟更新频率较低: {recent_count}次")
        else:
            logger.info("✅ 价格更新正常")
    
    async def _generate_final_report(self):
        """生成最终测试报告"""
        if not self.price_updates:
            logger.error("❌ 测试期间没有收到任何价格更新")
            return
        
        current_time = time.time()
        total_duration = current_time - self.test_start_time
        total_updates = len(self.price_updates)
        
        # 计算统计信息
        avg_updates_per_minute = (total_updates / total_duration) * 60
        
        # 检查数据间隔
        intervals = []
        for i in range(1, len(self.price_updates)):
            interval = self.price_updates[i]['timestamp'] - self.price_updates[i-1]['timestamp']
            intervals.append(interval)
        
        if intervals:
            avg_interval = sum(intervals) / len(intervals)
            max_interval = max(intervals)
            min_interval = min(intervals)
        else:
            avg_interval = max_interval = min_interval = 0
        
        # 检查长时间无更新的情况
        long_gaps = [interval for interval in intervals if interval > 60]  # 超过1分钟的间隔
        
        logger.info("=" * 60)
        logger.info("📊 Lighter连接稳定性测试报告")
        logger.info("=" * 60)
        logger.info(f"测试时长: {total_duration/60:.1f} 分钟")
        logger.info(f"总更新次数: {total_updates}")
        logger.info(f"平均更新频率: {avg_updates_per_minute:.1f} 次/分钟")
        logger.info(f"平均更新间隔: {avg_interval:.1f} 秒")
        logger.info(f"最大更新间隔: {max_interval:.1f} 秒")
        logger.info(f"最小更新间隔: {min_interval:.1f} 秒")
        logger.info(f"长时间无更新次数 (>60秒): {len(long_gaps)}")
        
        if self.client:
            status = self.client.status
            logger.info(f"最终连接状态: {status.get('connection_status', 'unknown')}")
            logger.info(f"最终连接健康: {status.get('connection_health', 'unknown')}")
            logger.info(f"最终连接质量: {status.get('connection_quality', 0)}/100")
            logger.info(f"总重连次数: {status.get('reconnect_attempts', 0)}")
        
        # 评估测试结果
        if len(long_gaps) == 0 and avg_updates_per_minute > 1:
            logger.info("✅ 测试结果: 连接稳定，修复效果良好")
        elif len(long_gaps) <= 2 and avg_updates_per_minute > 0.5:
            logger.info("⚠️ 测试结果: 连接基本稳定，偶有中断")
        else:
            logger.info("❌ 测试结果: 连接不稳定，需要进一步优化")
        
        logger.info("=" * 60)

async def main():
    """主函数"""
    tester = LighterFixTester()
    
    try:
        # 运行10分钟的稳定性测试
        await tester.test_connection_stability(duration_minutes=10)
        
    except KeyboardInterrupt:
        logger.info("🛑 测试被用户中断")
        if tester.client:
            await tester.client.close()
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        if tester.client:
            await tester.client.close()

if __name__ == "__main__":
    asyncio.run(main())
