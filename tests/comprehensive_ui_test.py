#!/usr/bin/env python3
"""
全面的UI测试脚本 - 使用Playwright MCP发现和诊断问题
"""

import asyncio
import json
import time
from datetime import datetime
from playwright.async_api import async_playwright
import structlog

logger = structlog.get_logger(__name__)

class ComprehensiveUITest:
    def __init__(self):
        self.browser = None
        self.page = None
        self.test_results = {}
        self.issues_found = []
        self.performance_metrics = {}
        
    async def setup_browser(self):
        """设置浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,  # 显示浏览器窗口以便观察
            args=['--start-maximized']
        )
        self.page = await self.browser.new_page()
        await self.page.set_viewport_size({"width": 1920, "height": 1080})
        
        # 启用控制台日志监听
        self.page.on("console", self._handle_console_message)
        self.page.on("pageerror", self._handle_page_error)
        
    def _handle_console_message(self, msg):
        """处理控制台消息"""
        if msg.type == "error":
            self.issues_found.append({
                "type": "console_error",
                "message": msg.text,
                "timestamp": datetime.now().isoformat()
            })
            logger.error(f"控制台错误: {msg.text}")
        elif msg.type == "warning":
            self.issues_found.append({
                "type": "console_warning", 
                "message": msg.text,
                "timestamp": datetime.now().isoformat()
            })
            logger.warning(f"控制台警告: {msg.text}")
            
    def _handle_page_error(self, error):
        """处理页面错误"""
        self.issues_found.append({
            "type": "page_error",
            "message": str(error),
            "timestamp": datetime.now().isoformat()
        })
        logger.error(f"页面错误: {error}")
        
    async def test_page_load_performance(self):
        """测试页面加载性能"""
        logger.info("🚀 测试页面加载性能...")
        
        start_time = time.time()
        
        try:
            # 导航到页面
            await self.page.goto("http://localhost:8000", wait_until="networkidle")
            
            load_time = time.time() - start_time
            
            # 检查页面标题
            title = await self.page.title()
            
            # 等待关键元素加载
            await self.page.wait_for_selector(".container-fluid", timeout=10000)
            
            total_load_time = time.time() - start_time
            
            self.performance_metrics["page_load"] = {
                "network_load_time": load_time,
                "total_load_time": total_load_time,
                "page_title": title,
                "status": "success" if total_load_time < 5 else "slow"
            }
            
            if total_load_time > 5:
                self.issues_found.append({
                    "type": "performance_issue",
                    "message": f"页面加载时间过长: {total_load_time:.2f}秒",
                    "timestamp": datetime.now().isoformat()
                })
                
            logger.info(f"✅ 页面加载完成 - 总时间: {total_load_time:.2f}秒")
            
        except Exception as e:
            self.issues_found.append({
                "type": "page_load_error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            })
            logger.error(f"❌ 页面加载失败: {e}")
            
    async def test_ui_elements_presence(self):
        """测试UI元素是否存在"""
        logger.info("🔍 测试UI元素存在性...")
        
        # 定义需要检查的关键元素
        critical_elements = {
            "系统状态卡片": ".status-card",
            "价格显示区域": ".price-display",
            "价差图表": "#spreadChart",
            "交易记录容器": "#tradesContainer",
            "当前挂单容器": "#ordersContainer",
            "系统日志": "#systemLogs",
            "连接状态": "#connectionStatus",
            "紧急停止按钮": "#emergencyStopBtn"
        }
        
        missing_elements = []
        
        for element_name, selector in critical_elements.items():
            try:
                element = await self.page.wait_for_selector(selector, timeout=5000)
                if element:
                    logger.info(f"✅ {element_name} 存在")
                else:
                    missing_elements.append(element_name)
                    logger.warning(f"❌ {element_name} 不存在")
            except Exception as e:
                missing_elements.append(element_name)
                self.issues_found.append({
                    "type": "missing_element",
                    "element": element_name,
                    "selector": selector,
                    "message": str(e),
                    "timestamp": datetime.now().isoformat()
                })
                logger.error(f"❌ {element_name} 查找失败: {e}")
                
        self.test_results["ui_elements"] = {
            "total_elements": len(critical_elements),
            "found_elements": len(critical_elements) - len(missing_elements),
            "missing_elements": missing_elements
        }
        
    async def test_api_endpoints(self):
        """测试API端点"""
        logger.info("🌐 测试API端点...")
        
        api_endpoints = [
            "/api/status",
            "/api/prices", 
            "/api/trades",
            "/api/orders",
            "/api/risk",
            "/api/performance"
        ]
        
        api_results = {}
        
        for endpoint in api_endpoints:
            try:
                response = await self.page.evaluate(f"""
                    async () => {{
                        try {{
                            const response = await fetch('{endpoint}');
                            return {{
                                ok: response.ok,
                                status: response.status,
                                data: response.ok ? await response.json() : null
                            }};
                        }} catch (e) {{
                            return {{
                                ok: false,
                                error: e.message
                            }};
                        }}
                    }}
                """)
                
                api_results[endpoint] = response
                
                if response.get("ok"):
                    logger.info(f"✅ {endpoint} 正常")
                else:
                    self.issues_found.append({
                        "type": "api_error",
                        "endpoint": endpoint,
                        "status": response.get("status"),
                        "error": response.get("error"),
                        "timestamp": datetime.now().isoformat()
                    })
                    logger.error(f"❌ {endpoint} 失败: {response}")
                    
            except Exception as e:
                self.issues_found.append({
                    "type": "api_test_error",
                    "endpoint": endpoint,
                    "message": str(e),
                    "timestamp": datetime.now().isoformat()
                })
                logger.error(f"❌ {endpoint} 测试失败: {e}")
                
        self.test_results["api_endpoints"] = api_results
        
    async def test_websocket_connection(self):
        """测试WebSocket连接"""
        logger.info("🔌 测试WebSocket连接...")
        
        try:
            # 检查WebSocket连接状态
            ws_status = await self.page.evaluate("""
                () => {
                    return {
                        websocket_exists: typeof ws !== 'undefined',
                        websocket_state: typeof ws !== 'undefined' ? ws.readyState : null,
                        websocket_url: typeof ws !== 'undefined' ? ws.url : null
                    };
                }
            """)
            
            self.test_results["websocket"] = ws_status
            
            if ws_status.get("websocket_exists"):
                state = ws_status.get("websocket_state")
                if state == 1:  # WebSocket.OPEN
                    logger.info("✅ WebSocket连接正常")
                else:
                    self.issues_found.append({
                        "type": "websocket_issue",
                        "message": f"WebSocket状态异常: {state}",
                        "timestamp": datetime.now().isoformat()
                    })
                    logger.warning(f"⚠️ WebSocket状态: {state}")
            else:
                self.issues_found.append({
                    "type": "websocket_missing",
                    "message": "WebSocket对象不存在",
                    "timestamp": datetime.now().isoformat()
                })
                logger.error("❌ WebSocket对象不存在")
                
        except Exception as e:
            self.issues_found.append({
                "type": "websocket_test_error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            })
            logger.error(f"❌ WebSocket测试失败: {e}")

    async def test_data_display_accuracy(self):
        """测试数据显示准确性"""
        logger.info("📊 测试数据显示准确性...")

        try:
            # 获取API数据
            api_data = await self.page.evaluate("""
                async () => {
                    try {
                        const [statusRes, pricesRes, tradesRes] = await Promise.all([
                            fetch('/api/status'),
                            fetch('/api/prices'),
                            fetch('/api/trades?limit=5')
                        ]);

                        return {
                            status: statusRes.ok ? await statusRes.json() : null,
                            prices: pricesRes.ok ? await pricesRes.json() : null,
                            trades: tradesRes.ok ? await tradesRes.json() : null
                        };
                    } catch (e) {
                        return { error: e.message };
                    }
                }
            """)

            # 获取页面显示的数据
            page_data = await self.page.evaluate("""
                () => {
                    const getTextContent = (selector) => {
                        const element = document.querySelector(selector);
                        return element ? element.textContent.trim() : null;
                    };

                    return {
                        total_assets: getTextContent('#totalAssets'),
                        total_profit: getTextContent('#totalProfit'),
                        trade_count: getTextContent('#tradeCount'),
                        success_rate: getTextContent('#successRate'),
                        btc_price: getTextContent('#btcPrice'),
                        binance_price: getTextContent('#binancePrice'),
                        lighter_price: getTextContent('#lighterPrice')
                    };
                }
            """)

            # 比较数据一致性
            data_consistency = []

            if api_data.get("status") and page_data:
                api_status = api_data["status"]

                # 检查各项数据是否一致
                stats = api_status.get("stats", {})
                checks = [
                    ("总资产", api_status.get("total_assets"), page_data.get("total_assets")),
                    ("总盈亏", stats.get("total_profit"), page_data.get("total_profit")),
                    ("交易次数", str(stats.get("total_trades", 0)), page_data.get("trade_count")),
                    ("成功率", f"{stats.get('win_rate', 0):.1f}%", page_data.get("success_rate"))
                ]

                for name, api_value, page_value in checks:
                    if api_value and page_value:
                        # 简单的字符串包含检查
                        if str(api_value) in page_value or page_value in str(api_value):
                            data_consistency.append({"field": name, "status": "consistent"})
                            logger.info(f"✅ {name} 数据一致")
                        else:
                            data_consistency.append({
                                "field": name,
                                "status": "inconsistent",
                                "api_value": api_value,
                                "page_value": page_value
                            })
                            self.issues_found.append({
                                "type": "data_inconsistency",
                                "field": name,
                                "api_value": api_value,
                                "page_value": page_value,
                                "timestamp": datetime.now().isoformat()
                            })
                            logger.warning(f"⚠️ {name} 数据不一致: API={api_value}, 页面={page_value}")
                    else:
                        data_consistency.append({
                            "field": name,
                            "status": "missing_data",
                            "api_value": api_value,
                            "page_value": page_value
                        })
                        logger.warning(f"⚠️ {name} 数据缺失")

            self.test_results["data_consistency"] = data_consistency

        except Exception as e:
            self.issues_found.append({
                "type": "data_accuracy_test_error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            })
            logger.error(f"❌ 数据准确性测试失败: {e}")

    async def test_interactive_elements(self):
        """测试交互元素"""
        logger.info("🖱️ 测试交互元素...")

        interactive_tests = []

        try:
            # 测试刷新按钮
            refresh_btn = await self.page.query_selector("#refreshTradesBtn")
            if refresh_btn:
                await refresh_btn.click()
                await self.page.wait_for_timeout(1000)
                interactive_tests.append({"element": "刷新交易记录按钮", "status": "clickable"})
                logger.info("✅ 刷新交易记录按钮可点击")
            else:
                interactive_tests.append({"element": "刷新交易记录按钮", "status": "not_found"})
                self.issues_found.append({
                    "type": "missing_interactive_element",
                    "element": "刷新交易记录按钮",
                    "timestamp": datetime.now().isoformat()
                })

            # 测试刷新挂单按钮
            refresh_orders_btn = await self.page.query_selector("#refreshOrdersBtn")
            if refresh_orders_btn:
                await refresh_orders_btn.click()
                await self.page.wait_for_timeout(1000)
                interactive_tests.append({"element": "刷新挂单按钮", "status": "clickable"})
                logger.info("✅ 刷新挂单按钮可点击")
            else:
                interactive_tests.append({"element": "刷新挂单按钮", "status": "not_found"})

            # 测试清除日志按钮
            clear_logs_btn = await self.page.query_selector("#clearLogsBtn")
            if clear_logs_btn:
                await clear_logs_btn.click()
                await self.page.wait_for_timeout(1000)
                interactive_tests.append({"element": "清除日志按钮", "status": "clickable"})
                logger.info("✅ 清除日志按钮可点击")
            else:
                interactive_tests.append({"element": "清除日志按钮", "status": "not_found"})

            self.test_results["interactive_elements"] = interactive_tests

        except Exception as e:
            self.issues_found.append({
                "type": "interactive_test_error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            })
            logger.error(f"❌ 交互元素测试失败: {e}")

    async def test_real_time_updates(self):
        """测试实时更新功能"""
        logger.info("⏱️ 测试实时更新功能...")

        try:
            # 记录初始状态
            initial_state = await self.page.evaluate("""
                () => {
                    const getTextContent = (selector) => {
                        const element = document.querySelector(selector);
                        return element ? element.textContent.trim() : null;
                    };

                    return {
                        timestamp: Date.now(),
                        total_profit: getTextContent('#totalProfit'),
                        trade_count: getTextContent('#tradeCount'),
                        btc_price: getTextContent('#btcPrice'),
                        last_update: getTextContent('#lastUpdate')
                    };
                }
            """)

            # 等待一段时间观察更新
            await self.page.wait_for_timeout(15000)  # 等待15秒

            # 记录更新后状态
            updated_state = await self.page.evaluate("""
                () => {
                    const getTextContent = (selector) => {
                        const element = document.querySelector(selector);
                        return element ? element.textContent.trim() : null;
                    };

                    return {
                        timestamp: Date.now(),
                        total_profit: getTextContent('#totalProfit'),
                        trade_count: getTextContent('#tradeCount'),
                        btc_price: getTextContent('#btcPrice'),
                        last_update: getTextContent('#lastUpdate')
                    };
                }
            """)

            # 检查是否有更新
            updates_detected = []

            for field in ['total_profit', 'trade_count', 'btc_price', 'last_update']:
                initial_value = initial_state.get(field)
                updated_value = updated_state.get(field)

                if initial_value != updated_value:
                    updates_detected.append({
                        "field": field,
                        "initial": initial_value,
                        "updated": updated_value,
                        "changed": True
                    })
                    logger.info(f"✅ {field} 已更新: {initial_value} -> {updated_value}")
                else:
                    updates_detected.append({
                        "field": field,
                        "value": initial_value,
                        "changed": False
                    })

            self.test_results["real_time_updates"] = {
                "observation_duration": 15,
                "updates_detected": updates_detected,
                "any_updates": any(update["changed"] for update in updates_detected)
            }

            if not any(update["changed"] for update in updates_detected):
                self.issues_found.append({
                    "type": "no_real_time_updates",
                    "message": "在15秒观察期内未检测到任何实时更新",
                    "timestamp": datetime.now().isoformat()
                })
                logger.warning("⚠️ 未检测到实时更新")
            else:
                logger.info("✅ 检测到实时更新")

        except Exception as e:
            self.issues_found.append({
                "type": "real_time_test_error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            })
            logger.error(f"❌ 实时更新测试失败: {e}")

    async def generate_comprehensive_report(self):
        """生成全面的测试报告"""
        logger.info("📋 生成全面测试报告...")

        # 计算测试统计
        total_issues = len(self.issues_found)
        critical_issues = len([issue for issue in self.issues_found if issue["type"] in ["page_load_error", "api_error", "websocket_missing"]])
        warning_issues = len([issue for issue in self.issues_found if issue["type"] in ["console_warning", "data_inconsistency", "no_real_time_updates"]])

        # 生成问题分类统计
        issue_categories = {}
        for issue in self.issues_found:
            category = issue["type"]
            if category not in issue_categories:
                issue_categories[category] = 0
            issue_categories[category] += 1

        # 生成测试总结
        test_summary = {
            "total_tests_run": len(self.test_results),
            "total_issues_found": total_issues,
            "critical_issues": critical_issues,
            "warning_issues": warning_issues,
            "issue_categories": issue_categories,
            "overall_status": "CRITICAL" if critical_issues > 0 else "WARNING" if warning_issues > 0 else "HEALTHY"
        }

        # 生成完整报告
        comprehensive_report = {
            "test_timestamp": datetime.now().isoformat(),
            "test_method": "Playwright MCP全面UI测试",
            "test_summary": test_summary,
            "performance_metrics": self.performance_metrics,
            "test_results": self.test_results,
            "issues_found": self.issues_found,
            "recommendations": self._generate_recommendations()
        }

        # 保存报告
        with open("comprehensive_ui_test_report.json", "w", encoding="utf-8") as f:
            json.dump(comprehensive_report, f, indent=2, ensure_ascii=False)

        logger.info("✅ 全面测试报告已保存: comprehensive_ui_test_report.json")

        # 打印测试总结
        print("\n" + "="*80)
        print("🎯 全面UI测试总结")
        print("="*80)
        print(f"📊 测试状态: {test_summary['overall_status']}")
        print(f"🔍 总测试项: {test_summary['total_tests_run']}")
        print(f"⚠️ 发现问题: {total_issues} 个")
        print(f"🚨 严重问题: {critical_issues} 个")
        print(f"⚡ 警告问题: {warning_issues} 个")

        if issue_categories:
            print("\n📋 问题分类:")
            for category, count in issue_categories.items():
                print(f"   • {category}: {count} 个")

        if self.issues_found:
            print("\n🔍 主要问题:")
            for i, issue in enumerate(self.issues_found[:5], 1):  # 显示前5个问题
                print(f"   {i}. [{issue['type']}] {issue['message']}")

        print("\n📄 详细报告已保存到: comprehensive_ui_test_report.json")
        print("="*80)

        return comprehensive_report

    def _generate_recommendations(self):
        """生成修复建议"""
        recommendations = []

        # 基于发现的问题生成建议
        issue_types = [issue["type"] for issue in self.issues_found]

        if "page_load_error" in issue_types:
            recommendations.append("检查Web服务器是否正常运行，确保端口8000可访问")

        if "missing_element" in issue_types:
            recommendations.append("检查HTML模板中的元素ID是否正确，确保关键UI元素存在")

        if "api_error" in issue_types:
            recommendations.append("检查API端点是否正常工作，验证后端服务状态")

        if "websocket_missing" in issue_types or "websocket_issue" in issue_types:
            recommendations.append("检查WebSocket连接配置，确保实时数据推送正常")

        if "data_inconsistency" in issue_types:
            recommendations.append("检查前端数据显示逻辑，确保与API数据一致")

        if "no_real_time_updates" in issue_types:
            recommendations.append("检查实时更新机制，确保WebSocket推送和前端更新正常")

        if "console_error" in issue_types:
            recommendations.append("检查浏览器控制台错误，修复JavaScript问题")

        if "performance_issue" in issue_types:
            recommendations.append("优化页面加载性能，减少资源加载时间")

        if not recommendations:
            recommendations.append("系统运行良好，建议定期进行UI测试以确保稳定性")

        return recommendations

    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
        logger.info("🧹 测试资源清理完成")

async def main():
    """主函数"""
    logger.info("🚀 启动全面UI测试...")

    test = ComprehensiveUITest()

    try:
        # 设置浏览器
        await test.setup_browser()

        # 执行所有测试
        await test.test_page_load_performance()
        await test.test_ui_elements_presence()
        await test.test_api_endpoints()
        await test.test_websocket_connection()
        await test.test_data_display_accuracy()
        await test.test_interactive_elements()
        await test.test_real_time_updates()

        # 生成报告
        await test.generate_comprehensive_report()

        logger.info("🎉 全面UI测试完成！")

    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
    finally:
        await test.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
