# API 参考文档

## 📋 概述

本文档描述了 Binance-Lighter 套利交易系统的 Web API 接口。所有 API 端点都基于 RESTful 设计，返回 JSON 格式数据。

**基础URL**: `http://localhost:8000` (默认端口)

## 🔗 API 端点

### 1. 系统状态

#### GET `/api/status`
获取系统整体状态信息

**响应示例**:
```json
{
  "status": "running",
  "uptime": 3600,
  "binance_connected": true,
  "lighter_connected": true,
  "paper_trading": true,
  "emergency_stop": false,
  "total_trades": 15,
  "total_profit": 125.50,
  "timestamp": 1704067200
}
```

### 2. 实时价格数据

#### GET `/api/prices`
获取实时价格和价差数据

**响应示例**:
```json
{
  "binance_prices": {
    "bid": "43250.50",
    "ask": "43251.00",
    "last": "43250.75"
  },
  "lighter_prices": {
    "bid": "43252.00",
    "ask": "43252.50",
    "last": "43252.25"
  },
  "spread_ma_value": 0.000035,
  "ma_period": 20,
  "current_signal": {
    "signal_type": "buy",
    "confidence": 0.85,
    "timestamp": 1704067200
  },
  "timestamp": 1704067200
}
```

### 3. 交易记录

#### GET `/api/trades`
获取交易历史记录

**查询参数**:
- `limit` (可选): 返回记录数量，默认20
- `offset` (可选): 偏移量，默认0

**响应示例**:
```json
{
  "success": true,
  "trades": [
    {
      "id": 1,
      "timestamp": 1704067200,
      "buy_exchange": "binance",
      "sell_exchange": "lighter",
      "buy_price": 43250.50,
      "sell_price": 43252.25,
      "amount": 0.01,
      "profit": 0.0175,
      "status": "completed"
    }
  ],
  "total": 15,
  "count": 1
}
```

### 4. 当前挂单 **（新增）**

#### GET `/api/orders`
获取当前挂单信息

**响应示例**:
```json
{
  "success": true,
  "orders": [
    {
      "id": "order_123",
      "exchange": "Binance",
      "symbol": "BTC/USDT",
      "side": "buy",
      "amount": 0.01,
      "price": 43250.50,
      "filled_amount": 0.005,
      "status": "open",
      "timestamp": 1704067200
    }
  ],
  "count": 1
}
```

### 5. 风险状态

#### GET `/api/risk`
获取风险管理状态

**响应示例**:
```json
{
  "risk_score": 3.2,
  "max_position_size": 0.1,
  "current_position": 0.05,
  "daily_pnl": 125.50,
  "max_daily_loss": 1000,
  "drawdown": 0.02,
  "max_drawdown": 0.05,
  "emergency_stop": false
}
```

### 6. 性能指标

#### GET `/api/performance`
获取系统性能指标

**响应示例**:
```json
{
  "cpu_usage": 4.5,
  "memory_usage": 150.2,
  "network_latency": {
    "binance": 45,
    "lighter": 52
  },
  "trade_execution_time": 1.2,
  "uptime": 3600,
  "error_rate": 0.01
}
```

### 7. 系统控制

#### POST `/api/emergency_stop`
触发紧急停止

**请求体**:
```json
{
  "reason": "Manual stop"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "Emergency stop activated",
  "timestamp": 1704067200
}
```

#### POST `/api/reset_emergency`
重置紧急停止状态

**响应示例**:
```json
{
  "success": true,
  "message": "Emergency stop reset",
  "timestamp": 1704067200
}
```

### 8. 策略参数

#### GET `/api/strategy/parameters`
获取当前策略参数

**响应示例**:
```json
{
  "ma_period": 20,
  "min_profit_threshold": 0.001,
  "max_position_size": 0.1,
  "stop_loss_pct": 0.02,
  "max_spread_threshold": 0.02
}
```

#### POST `/api/strategy/parameters`
更新策略参数

**请求体**:
```json
{
  "ma_period": 25,
  "min_profit_threshold": 0.0015
}
```

### 9. 数据管理 **（新增 v1.2.0）**

#### GET `/api/reset_status`
获取数据重置状态信息（仅模拟交易模式）

**响应示例**:
```json
{
  "success": true,
  "is_paper_trading": true,
  "reset_available": true,
  "current_stats": {
    "trades": 156,
    "prices": 50000,
    "spreads": 10000,
    "risk_metrics": 500,
    "system_status": 200,
    "performance_metrics": 50
  },
  "total_records": 60906,
  "recent_backups": [
    "arbitrage_backup_20250601_051133.db",
    "arbitrage_backup_20250601_051118.db"
  ],
  "backup_directory": "data/backups"
}
```

#### POST `/api/reset_data`
执行数据重置操作（仅模拟交易模式）

**查询参数**:
- `reset_type`: 重置类型，`trading`（仅交易数据）或 `all`（全部数据）
- `create_backup`: 是否创建备份，默认 `true`

**响应示例**:
```json
{
  "success": true,
  "reset_type": "trading",
  "reset_result": {
    "success": true,
    "timestamp": "2025-06-01T05:11:18.123Z",
    "backup_created": true,
    "backup_path": "data/backups/arbitrage_backup_20250601_051118.db",
    "stats_before": {
      "trades": 156,
      "risk_metrics": 500,
      "performance_metrics": 50
    },
    "stats_after": {
      "trades": 0,
      "risk_metrics": 0,
      "performance_metrics": 0
    },
    "total_records_deleted": 706,
    "message": "交易数据已重置，价格历史已保留"
  },
  "trading_restored": true,
  "message": "数据重置成功完成"
}
```

## 🔄 WebSocket 连接

### 连接端点
`ws://localhost:8000/ws`

### 消息格式

#### 价格更新
```json
{
  "type": "price_update",
  "data": {
    "binance_prices": {...},
    "lighter_prices": {...},
    "spread_ma_value": 0.000035,
    "timestamp": 1704067200
  }
}
```

#### 交易更新
```json
{
  "type": "trade_update",
  "data": {
    "trade_id": 1,
    "status": "completed",
    "profit": 0.0175,
    "timestamp": 1704067200
  }
}
```

#### 系统状态更新
```json
{
  "type": "status_update",
  "data": {
    "status": "running",
    "emergency_stop": false,
    "timestamp": 1704067200
  }
}
```

#### 数据重置更新 **（新增 v1.2.0）**
```json
{
  "type": "data_reset",
  "data": {
    "reset_type": "trading",
    "message": "数据重置完成 - 交易数据已重置，价格历史已保留",
    "backup_created": true,
    "total_deleted": 706,
    "timestamp": 1704067200
  }
}
```

## 📊 错误处理

### 错误响应格式
```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "timestamp": 1704067200
}
```

### 常见错误代码
- `SYSTEM_NOT_READY`: 系统未就绪
- `INVALID_PARAMETERS`: 参数无效
- `EXCHANGE_ERROR`: 交易所连接错误
- `INSUFFICIENT_FUNDS`: 资金不足
- `EMERGENCY_STOP_ACTIVE`: 紧急停止激活
- `RESET_NOT_AVAILABLE`: 重置功能不可用（非模拟交易模式）
- `DATABASE_ERROR`: 数据库操作错误

## 🔒 安全说明

1. **本地访问**: API 仅限本地访问，不对外网开放
2. **无认证**: 当前版本无需认证，仅供本地监控使用
3. **数据验证**: 所有输入数据都经过验证和清理
4. **错误脱敏**: 错误信息不包含敏感数据

## 📝 使用示例

### Python 示例
```python
import requests

# 获取系统状态
response = requests.get('http://localhost:8000/api/status')
status = response.json()
print(f"系统状态: {status['status']}")

# 获取实时价格
response = requests.get('http://localhost:8000/api/prices')
prices = response.json()
print(f"Binance价格: {prices['binance_prices']['last']}")

# 重置交易数据（仅模拟交易模式）
response = requests.post(
    'http://localhost:8000/api/reset_data',
    params={'reset_type': 'trading', 'create_backup': True}
)
result = response.json()
print(f"重置结果: {result['message']}")
```

### JavaScript 示例
```javascript
// 获取交易记录
fetch('/api/trades?limit=10')
  .then(response => response.json())
  .then(data => {
    console.log('交易记录:', data.trades);
  });

// WebSocket 连接
const ws = new WebSocket('ws://localhost:8000/ws');
ws.onmessage = function(event) {
  const message = JSON.parse(event.data);
  console.log('收到消息:', message);
};

// 重置数据（仅模拟交易模式）
fetch('/api/reset_data?reset_type=trading&create_backup=true', {
  method: 'POST'
})
.then(response => response.json())
.then(data => console.log('重置结果:', data.message));
```

---

**文档版本**: v1.2.0
**最后更新**: 2025-06-01
**API版本**: v1.2.0
