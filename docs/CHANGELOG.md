# 更新日志

## [v1.2.0] - 2025-06-01

### 🎯 重大优化

#### 策略逻辑修复
- **修正价差计算逻辑**: 修复基于MA比较的错误逻辑，改为基于实际价格差异
- **信号生成优化**: 确保只在有利可图时生成交易信号
- **预期利润验证**: 添加预期利润检查机制，过滤负收益交易

#### 参数全面优化
- **最小盈利阈值**: 从 0.0000001 提升到 0.001，确保覆盖交易费用
- **冷却期延长**: 从 10秒 延长到 60秒，减少无效交易
- **交易量控制**: 最大交易量从 0.01 BTC 降低到 0.005 BTC
- **仓位限制**: 从 80% 严格控制到 30%
- **止损优化**: 止损比例从 5% 降低到 2%，更快止损
- **信心度提升**: 信号信心度要求从 30% 提升到 50%

### 🔄 新增数据重置功能

#### 重置模式
- **重置交易数据**: 仅删除交易记录、风险指标、性能指标，保留价格历史
- **重置全部数据**: 完全清空所有数据表，重新开始
- **智能备份**: 重置前自动创建数据库备份

#### 安全机制
- **模式限制**: 仅在模拟交易模式下可用
- **多重确认**: 详细说明和确认流程
- **实时进度**: 重置过程实时反馈
- **操作审计**: 完整的重置操作日志

#### 用户界面
- **数据管理面板**: 在系统控制区域添加数据重置按钮
- **重置模态框**: 详细的重置信息和选项设置
- **统计显示**: 当前数据库记录统计
- **备份管理**: 最近备份文件列表

### 🔧 API扩展

#### 新增端点
- **`GET /api/reset_status`**: 获取数据重置状态信息
  - 当前数据统计
  - 重置功能可用性
  - 最近备份文件列表
- **`POST /api/reset_data`**: 执行数据重置操作
  - 支持重置类型选择（trading/all）
  - 支持备份选项控制
  - 返回详细重置结果

#### 数据库层增强
- **`reset_all_data()`**: 重置所有数据的方法
- **`reset_trading_data_only()`**: 仅重置交易数据的方法
- **`reset_stats()`**: 重置交易记录器统计的方法

### 📊 优化效果

#### 立即效果
- ✅ 停止负收益交易生成
- ✅ 风险控制机制生效
- ✅ 系统稳定运行
- ✅ 数据管理功能完善

#### 预期改善
- **成功率**: 从 16.31% 提升到 60%+
- **月收益率**: 从负收益转为 2-5% 正收益
- **最大回撤**: 控制在 5% 以内
- **交易质量**: 显著提升信号质量

### 🛡️ 风险控制增强
- **严格仓位控制**: 最大仓位比例降低到 30%
- **快速止损机制**: 2% 止损比例
- **日亏损限制**: 降低到 50 USDT
- **最大敞口控制**: 限制在 1000 USDT

---

## [v1.1.0] - 2025-06-01

### 🎨 界面优化

#### 新增功能
- **统一面板高度**: 实时价格和价差走势面板高度统一为350px，提升视觉一致性
- **价差MA计算公式显示**: 在实时价格面板中新增详细的价差MA计算公式展示
  - 当前价差率实时显示
  - MA周期参数显示（默认20）
  - 详细计算公式：`MA = Σ(价差率) / 周期`
  - 价差率公式：`(Lighter价格 - Binance价格) / Binance价格`
- **分离交易记录**: 将交易记录拆分为两个独立面板
  - 当前挂单面板：显示未完成订单状态
  - 成交记录面板：显示历史交易记录
  - 两个面板并排显示，各占50%宽度

#### 界面改进
- 所有面板添加 `h-100` 类确保高度一致
- 价差走势图表容器设置 `height: 100%` 充分利用空间
- 挂单状态用不同颜色的徽章显示（挂单中/已成交/已取消）
- 独立刷新按钮，每个面板都有手动刷新功能

### 🔧 后端增强

#### 新增API
- **`GET /api/orders`**: 获取当前挂单信息
  - 支持Binance和Lighter两个交易所的挂单查询
  - 返回订单ID、交易所、交易对、方向、数量、价格、状态等信息
  - 按时间排序（最新的在前）

#### API增强
- **`GET /api/prices`**: 增加MA周期信息
  - 新增 `ma_period` 字段，显示当前MA计算周期
  - 支持价差MA计算公式的实时显示

#### 功能优化
- 挂单信息每30秒自动刷新
- WebSocket实时推送挂单状态更新
- 价差MA计算公式实时数值更新

### 📊 前端功能

#### JavaScript增强
- 新增 `updateSpreadMAFormula()` 函数：实时更新价差MA计算公式显示
- 新增 `updateOrders()` 函数：处理挂单信息显示
- 新增 `loadOrders()` 函数：从API获取挂单数据
- 增强 `updatePricesOnly()` 函数：支持价差MA公式更新

#### 实时更新
- 挂单信息定期自动刷新（30秒间隔）
- 价差MA公式中的数值实时更新
- 独立的刷新按钮事件绑定

### 🧪 测试更新

#### 新增测试
- 统一面板高度验证测试
- 价差MA计算公式显示测试
- 分离交易记录面板测试
- 挂单API功能测试
- 实时更新机制测试

#### 测试脚本更新
- `playwright_verification.py`: 新增界面功能测试
- `playwright_monitoring.py`: 新增API端点测试
- `monitoring_test.py`: 更新界面元素验证

### 📚 文档更新

#### 文档增强
- 更新 `README.md`：添加最新界面功能描述
- 更新 `docs/SYSTEM_STATUS.md`：记录最新优化内容
- 新增 `docs/API_REFERENCE.md`：完整的API参考文档
- 更新 `tests/README.md`：添加新测试功能说明

#### 新增文档
- 创建详细的API参考文档
- 更新系统状态报告
- 完善测试文档说明

### 🔄 兼容性

#### 向后兼容
- 所有现有API保持兼容
- 原有功能完全保留
- 配置文件格式不变

#### 新增依赖
- 无新增外部依赖
- 使用现有技术栈实现

---

## [v1.0.0] - 2025-05-29

### 🚀 初始发布

#### 核心功能
- Binance和Lighter交易所集成
- 实时价格数据获取
- 套利策略执行
- 风险管理系统
- 模拟交易模式

#### Web监控界面
- 实时价格显示
- 交易记录查看
- 系统状态监控
- 风险指标显示
- 策略参数调整

#### 数据管理
- SQLite数据库集成
- 交易记录存储
- 价格历史数据
- 自动数据备份

#### 系统管理
- 进程锁机制
- 配置文件管理
- 日志系统
- 错误处理
- 性能监控

---

## 版本说明

### 版本号规则
- **主版本号**: 重大功能更新或架构变更
- **次版本号**: 新功能添加或重要改进
- **修订版本号**: 错误修复或小幅优化

### 更新类型
- 🚀 **新功能**: 全新功能添加
- 🎨 **界面优化**: UI/UX改进
- 🔧 **后端增强**: 后端功能改进
- 📊 **前端功能**: 前端功能增强
- 🧪 **测试更新**: 测试相关更新
- 📚 **文档更新**: 文档改进
- 🐛 **错误修复**: Bug修复
- ⚡ **性能优化**: 性能改进
- 🔒 **安全更新**: 安全相关更新

### 发布周期
- **主版本**: 根据重大功能需求发布
- **次版本**: 每月发布一次
- **修订版本**: 根据需要随时发布

---

**文档维护**: 开发团队
**最后更新**: 2025-06-01
**文档版本**: v1.2.0
