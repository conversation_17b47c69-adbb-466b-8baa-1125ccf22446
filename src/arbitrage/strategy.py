"""
套利策略模块

实现Binance-Lighter套利交易策略
"""

import asyncio
import time
from typing import Dict, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import structlog

from ..utils.indicators import SpreadAnalyzer, MovingAverage
from ..utils.logger import trading_logger

logger = structlog.get_logger(__name__)


class SignalType(Enum):
    """交易信号类型"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"


@dataclass
class ArbitrageSignal:
    """套利信号"""
    signal_type: SignalType
    binance_price: float
    lighter_price: float
    diff_rate: float
    ma_value: float
    confidence: float
    timestamp: float
    reason: str
    expected_profit: Optional[float] = None  # 预期利润
    spread_pct: Optional[float] = None  # 价差百分比


@dataclass
class OrderInfo:
    """订单信息"""
    exchange: str
    order_id: str
    symbol: str
    side: str
    amount: float
    price: float
    status: str
    timestamp: float
    trade_id: Optional[str] = None  # 添加交易ID字段


class ArbitrageStrategy:
    """套利交易策略"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化套利策略

        Args:
            config: 配置字典
        """
        self.config = config

        # 交易配置
        self.symbol = config.get('symbol', 'BTC/USDT')
        self.ma_period = config.get('ma_period', 20)
        self.min_profit_threshold = config.get('min_profit_threshold', 0.001)
        self.max_spread_threshold = config.get('max_spread_threshold', 0.02)
        self.max_trade_amount = config.get('max_trade_amount', 0.01)
        self.min_trade_amount = config.get('min_trade_amount', 0.001)

        # 时间窗口配置
        self.use_time_window = config.get('strategy', {}).get('parameters', {}).get('use_time_window', True)
        self.window_minutes = config.get('strategy', {}).get('parameters', {}).get('window_minutes', 5)

        # 初始化指标计算器
        self.spread_analyzer = SpreadAnalyzer(
            ma_period=self.ma_period,
            use_time_window=self.use_time_window,
            window_minutes=self.window_minutes
        )

        # 当前状态
        self.current_signal: Optional[ArbitrageSignal] = None
        self.active_orders: Dict[str, OrderInfo] = {}  # exchange -> OrderInfo
        self.last_trade_time = 0
        self.is_hedging = False

        # 价格数据
        self.binance_prices = {'bid': 0, 'ask': 0, 'last': 0}
        self.lighter_prices = {'bid': 0, 'ask': 0, 'last': 0}

        logger.info("套利策略初始化完成", symbol=self.symbol, ma_period=self.ma_period)

        # 清理可能残留的活跃订单（特别是在纸上交易模式下）
        self.clear_active_orders()

    def update_binance_prices(self, bid: float, ask: float, last: float = None) -> None:
        """
        更新Binance价格

        Args:
            bid: 买一价
            ask: 卖一价
            last: 最新成交价
        """
        self.binance_prices['bid'] = bid
        self.binance_prices['ask'] = ask
        if last:
            self.binance_prices['last'] = last

        logger.debug("Binance价格更新", **self.binance_prices)

    def update_lighter_prices(self, bid: float, ask: float, last: float = None) -> None:
        """
        更新Lighter价格

        Args:
            bid: 买一价
            ask: 卖一价
            last: 最新成交价
        """
        self.lighter_prices['bid'] = bid
        self.lighter_prices['ask'] = ask

        # 如果有真实成交价，使用真实成交价
        if last:
            self.lighter_prices['last'] = last
        else:
            # 如果没有真实成交价，使用买卖价中间价
            if bid > 0 and ask > 0:
                mid_price = (bid + ask) / 2
                self.lighter_prices['last'] = mid_price
                logger.debug("使用买卖价中间价作为Lighter最新价",
                           bid=bid, ask=ask, mid_price=mid_price)

        logger.debug("Lighter价格更新", **self.lighter_prices)

    def analyze_spread(self) -> Optional[ArbitrageSignal]:
        """
        分析价差并生成交易信号

        Returns:
            套利信号，如果没有信号则返回None
        """
        # 检查价格数据完整性
        if not self._validate_prices():
            return None

        # 使用最新成交价计算价差
        binance_price = self.binance_prices.get('last', 0)
        lighter_price = self.lighter_prices.get('last', 0)

        if binance_price <= 0 or lighter_price <= 0:
            return None

        # 更新价差分析器
        spread_data = self.spread_analyzer.update_spread(binance_price, lighter_price)

        if not spread_data or spread_data.get('ma') is None:
            return None

        diff_rate = spread_data['diff_rate']
        ma_value = spread_data['ma']

        # 生成交易信号
        signal = self._generate_signal(diff_rate, ma_value, binance_price, lighter_price)

        if signal:
            self.current_signal = signal
            logger.info("生成套利信号", signal=signal)

        return signal

    def _validate_prices(self) -> bool:
        """验证价格数据完整性"""
        binance_valid = all(self.binance_prices[key] > 0 for key in ['bid', 'ask'])
        lighter_valid = all(self.lighter_prices[key] > 0 for key in ['bid', 'ask'])
        return binance_valid and lighter_valid

    def _generate_signal(self, diff_rate: float, ma_value: float,
                        binance_price: float, lighter_price: float) -> Optional[ArbitrageSignal]:
        """
        修复后的信号生成逻辑 - 确保正向收益

        Args:
            diff_rate: 价差比例
            ma_value: 移动平均线值
            binance_price: Binance价格
            lighter_price: Lighter价格

        Returns:
            套利信号
        """
        # 检查价差是否超过最大阈值
        if abs(diff_rate) > self.max_spread_threshold:
            logger.warning("价差超过最大阈值", diff_rate=diff_rate, threshold=self.max_spread_threshold)
            return None

        # 计算实际价差 (绝对值)
        price_diff = abs(binance_price - lighter_price)

        # 计算最小盈利要求 (包含交易费用)
        min_profit_with_fees = self.min_profit_threshold + 0.0002  # 假设总费用0.02%

        # 检查是否满足最小盈利阈值
        if price_diff / min(binance_price, lighter_price) < min_profit_with_fees:
            return None

        # 修复后的信号逻辑 - 基于实际价格差异
        if binance_price < lighter_price:
            # Binance便宜，在Binance买入，在Lighter卖出
            signal_type = SignalType.BUY
            expected_profit = (lighter_price - binance_price) * self.min_trade_amount
            reason = f"Binance价格低于Lighter {price_diff:.2f} USDT"
        elif binance_price > lighter_price:
            # Lighter便宜，在Lighter买入，在Binance卖出
            signal_type = SignalType.SELL
            expected_profit = (binance_price - lighter_price) * self.min_trade_amount
            reason = f"Lighter价格低于Binance {price_diff:.2f} USDT"
        else:
            signal_type = SignalType.HOLD
            expected_profit = 0.0
            reason = "价格相等"

        # 只有预期利润为正时才生成信号
        if expected_profit <= 0:
            logger.debug("预期利润为负，跳过信号", expected_profit=expected_profit)
            return None

        # 计算信心度 (基于价差大小)
        confidence = min(price_diff / (binance_price * min_profit_with_fees), 1.0)

        # 最低信心度要求
        if confidence < 0.5:
            logger.debug("信心度不足，跳过信号", confidence=confidence)
            return None

        # 计算价差百分比
        spread_pct = price_diff / min(binance_price, lighter_price) * 100

        return ArbitrageSignal(
            signal_type=signal_type,
            binance_price=binance_price,
            lighter_price=lighter_price,
            diff_rate=diff_rate,
            ma_value=ma_value,
            confidence=confidence,
            timestamp=time.time(),
            reason=reason,
            expected_profit=expected_profit,
            spread_pct=spread_pct
        )

    def clear_expired_orders(self):
        """清理过期订单"""
        current_time = time.time()
        expired_orders = []

        for exchange, order_info in self.active_orders.items():
            # 检查订单是否过期（超过5分钟）
            if current_time - order_info.timestamp > 300:
                expired_orders.append(exchange)
                logger.info(f"发现过期订单: {exchange}, 订单时间: {order_info.timestamp}")

        for exchange in expired_orders:
            logger.info(f"清理过期订单: {exchange}")
            del self.active_orders[exchange]

    def should_place_order(self, signal: ArbitrageSignal) -> Tuple[bool, str]:
        """
        判断是否应该下单

        Args:
            signal: 套利信号

        Returns:
            (是否下单, 原因)
        """
        # 首先清理过期订单
        self.clear_expired_orders()

        # 检查是否正在对冲
        if self.is_hedging:
            return False, "正在对冲中"

        # 检查是否有活跃订单（改进后的检查）
        if self.active_orders:
            logger.debug("活跃订单详情", active_orders=self.active_orders)
            # 如果有活跃订单，检查是否可以进行对冲
            for exchange, order_info in self.active_orders.items():
                if order_info.status == 'filled':
                    # 如果订单已成交，可以进行对冲
                    logger.info(f"检测到已成交订单，可以对冲: {exchange}")
                    return True, f"可以对冲{exchange}的已成交订单"
            return False, "存在未成交的活跃订单"

        # 检查信号类型
        if signal.signal_type == SignalType.HOLD:
            return False, "信号为持有"

        # 检查信心度（降低阈值）
        if signal.confidence < 0.3:
            return False, f"信心度不足: {signal.confidence:.2f}"

        # 检查时间间隔（减少冷却时间）
        current_time = time.time()
        cooldown_period = self.config.get('strategy', {}).get('parameters', {}).get('cooldown_period', 10)
        if current_time - self.last_trade_time < cooldown_period:
            remaining_time = cooldown_period - (current_time - self.last_trade_time)
            return False, f"交易冷却时间未到，剩余{remaining_time:.1f}秒"

        return True, "满足下单条件"

    def calculate_order_amount(self, signal: ArbitrageSignal,
                             binance_bid_amount: float, lighter_bid_amount: float,
                             max_position: float) -> float:
        """
        计算订单数量

        Args:
            signal: 套利信号
            binance_bid_amount: Binance买一量
            lighter_bid_amount: Lighter买一量
            max_position: 最大持仓量

        Returns:
            订单数量
        """
        # 基础数量限制
        base_amount = min(self.max_trade_amount, max_position)

        # 考虑订单簿深度
        if signal.signal_type == SignalType.BUY:
            # 在Binance买入，限制为Binance买一量
            depth_limit = binance_bid_amount
        else:
            # 在Binance卖出，限制为Binance卖一量（这里用买一量作为近似）
            depth_limit = binance_bid_amount

        # 考虑对手盘深度
        counterpart_limit = lighter_bid_amount

        # 取最小值
        amount = min(base_amount, depth_limit, counterpart_limit)

        # 确保不小于最小交易量
        if amount < self.min_trade_amount:
            return 0

        return round(amount, 6)  # 保留6位小数

    def should_cancel_order(self, exchange: str, order_price: float) -> Tuple[bool, str]:
        """
        判断是否应该取消订单

        Args:
            exchange: 交易所名称
            order_price: 订单价格

        Returns:
            (是否取消, 原因)
        """
        if exchange not in self.active_orders:
            return False, "无活跃订单"

        order_info = self.active_orders[exchange]

        # 检查订单是否还在买一价
        if exchange == 'binance':
            current_best_price = self.binance_prices['bid'] if order_info.side == 'buy' else self.binance_prices['ask']
        else:
            current_best_price = self.lighter_prices['bid'] if order_info.side == 'buy' else self.lighter_prices['ask']

        if abs(order_price - current_best_price) > 0.0001:  # 价格偏差阈值
            return True, "订单价格不再是最优价格"

        # 检查信号是否仍然有效
        if self.current_signal:
            # 重新计算当前价差
            binance_price = self.binance_prices.get('last', 0)
            lighter_price = self.lighter_prices.get('last', 0)

            if binance_price > 0 and lighter_price > 0:
                current_diff_rate = (binance_price / lighter_price) - 1
                ma_value = self.spread_analyzer.spread_ma.get_current()

                if ma_value is not None:
                    # 检查信号方向是否仍然正确
                    if order_info.side == 'buy' and current_diff_rate >= ma_value:
                        return True, "买入信号失效"
                    elif order_info.side == 'sell' and current_diff_rate <= ma_value:
                        return True, "卖出信号失效"

        return False, "订单仍然有效"

    def add_active_order(self, exchange: str, order_info: OrderInfo) -> None:
        """添加活跃订单"""
        self.active_orders[exchange] = order_info
        logger.info("添加活跃订单", exchange=exchange, order_id=order_info.order_id)

    def remove_active_order(self, exchange: str) -> None:
        """移除活跃订单"""
        if exchange in self.active_orders:
            order_info = self.active_orders.pop(exchange)
            logger.info("移除活跃订单", exchange=exchange, order_id=order_info.order_id)

    def set_hedging_status(self, status: bool) -> None:
        """设置对冲状态"""
        self.is_hedging = status
        logger.info("对冲状态更新", is_hedging=status)

    def update_last_trade_time(self) -> None:
        """更新最后交易时间"""
        self.last_trade_time = time.time()

    def get_strategy_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        # 获取MA值和状态
        if self.use_time_window:
            # 时间窗口模式
            spread_ma_ready = self.spread_analyzer.time_analyzer.spread_ma.is_ready()
            spread_ma_value = self.spread_analyzer.time_analyzer.spread_ma.get_current()
        else:
            # 传统实时模式
            spread_ma_ready = self.spread_analyzer.spread_ma.is_ready()
            spread_ma_value = self.spread_analyzer.spread_ma.get_current()

        return {
            'symbol': self.symbol,
            'current_signal': self.current_signal,
            'active_orders': len(self.active_orders),
            'is_hedging': self.is_hedging,
            'last_trade_time': self.last_trade_time,
            'binance_prices': self.binance_prices,
            'lighter_prices': self.lighter_prices,
            'spread_ma_ready': spread_ma_ready,
            'spread_ma_value': spread_ma_value
        }

    def get_active_orders(self) -> Dict[str, OrderInfo]:
        """
        获取活跃订单

        Returns:
            活跃订单字典，键为交易所名称，值为订单信息
        """
        return self.active_orders.copy()

    def has_active_orders(self) -> bool:
        """
        检查是否有活跃订单

        Returns:
            是否有活跃订单
        """
        return len(self.active_orders) > 0

    def clear_active_orders(self) -> None:
        """
        清空所有活跃订单
        """
        if self.active_orders:
            logger.info("清空活跃订单", count=len(self.active_orders))
            self.active_orders.clear()

    def get_strategy_parameters(self) -> Dict[str, Any]:
        """
        获取当前策略参数

        Returns:
            策略参数字典
        """
        return {
            'symbol': self.symbol,
            'ma_period': self.ma_period,
            'min_profit_threshold': self.min_profit_threshold,
            'max_spread_threshold': self.max_spread_threshold,
            'max_trade_amount': self.max_trade_amount,
            'min_trade_amount': self.min_trade_amount,
            'cooldown_period': self.config.get('strategy', {}).get('parameters', {}).get('cooldown_period', 10),
            'signal_threshold': self.config.get('strategy', {}).get('parameters', {}).get('signal_threshold', 0.0001),
            'ma_fast': self.config.get('strategy', {}).get('parameters', {}).get('ma_fast', 10),
            'ma_slow': self.config.get('strategy', {}).get('parameters', {}).get('ma_slow', 20),
            'min_spread': self.config.get('strategy', {}).get('signal_filters', {}).get('min_spread', 0.0005),
            'max_spread': self.config.get('strategy', {}).get('signal_filters', {}).get('max_spread', 0.05),
            'min_volume': self.config.get('strategy', {}).get('signal_filters', {}).get('min_volume', 1.0),
            'use_time_window': self.use_time_window,
            'window_minutes': self.window_minutes
        }

    def update_strategy_parameters(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        动态更新策略参数

        Args:
            parameters: 要更新的参数字典

        Returns:
            更新结果字典
        """
        updated = []
        errors = []

        try:
            # 更新基础参数
            if 'ma_period' in parameters:
                new_value = int(parameters['ma_period'])
                if 5 <= new_value <= 100:
                    old_value = self.ma_period
                    self.ma_period = new_value
                    # 重新初始化价差分析器
                    self.spread_analyzer = SpreadAnalyzer(self.ma_period)
                    updated.append(f"ma_period: {old_value} -> {new_value}")
                    logger.info("更新MA周期", old_value=old_value, new_value=new_value)
                else:
                    errors.append("ma_period必须在5-100之间")

            if 'min_profit_threshold' in parameters:
                new_value = float(parameters['min_profit_threshold'])
                if 0.0001 <= new_value <= 0.1:
                    old_value = self.min_profit_threshold
                    self.min_profit_threshold = new_value
                    updated.append(f"min_profit_threshold: {old_value} -> {new_value}")
                    logger.info("更新最小盈利阈值", old_value=old_value, new_value=new_value)
                else:
                    errors.append("min_profit_threshold必须在0.0001-0.1之间")

            if 'max_spread_threshold' in parameters:
                new_value = float(parameters['max_spread_threshold'])
                if 0.001 <= new_value <= 0.5:
                    old_value = self.max_spread_threshold
                    self.max_spread_threshold = new_value
                    updated.append(f"max_spread_threshold: {old_value} -> {new_value}")
                    logger.info("更新最大价差阈值", old_value=old_value, new_value=new_value)
                else:
                    errors.append("max_spread_threshold必须在0.001-0.5之间")

            if 'max_trade_amount' in parameters:
                new_value = float(parameters['max_trade_amount'])
                if 0.001 <= new_value <= 10.0:
                    old_value = self.max_trade_amount
                    self.max_trade_amount = new_value
                    updated.append(f"max_trade_amount: {old_value} -> {new_value}")
                    logger.info("更新最大交易量", old_value=old_value, new_value=new_value)
                else:
                    errors.append("max_trade_amount必须在0.001-10.0之间")

            if 'min_trade_amount' in parameters:
                new_value = float(parameters['min_trade_amount'])
                if 0.0001 <= new_value <= 1.0:
                    old_value = self.min_trade_amount
                    self.min_trade_amount = new_value
                    updated.append(f"min_trade_amount: {old_value} -> {new_value}")
                    logger.info("更新最小交易量", old_value=old_value, new_value=new_value)
                else:
                    errors.append("min_trade_amount必须在0.0001-1.0之间")

            # 更新配置中的参数
            if 'cooldown_period' in parameters:
                new_value = int(parameters['cooldown_period'])
                if 1 <= new_value <= 300:
                    if 'strategy' not in self.config:
                        self.config['strategy'] = {}
                    if 'parameters' not in self.config['strategy']:
                        self.config['strategy']['parameters'] = {}

                    old_value = self.config['strategy']['parameters'].get('cooldown_period', 10)
                    self.config['strategy']['parameters']['cooldown_period'] = new_value
                    updated.append(f"cooldown_period: {old_value} -> {new_value}")
                    logger.info("更新冷却时间", old_value=old_value, new_value=new_value)
                else:
                    errors.append("cooldown_period必须在1-300秒之间")

            if 'signal_threshold' in parameters:
                new_value = float(parameters['signal_threshold'])
                if 0.00001 <= new_value <= 0.01:
                    if 'strategy' not in self.config:
                        self.config['strategy'] = {}
                    if 'parameters' not in self.config['strategy']:
                        self.config['strategy']['parameters'] = {}

                    old_value = self.config['strategy']['parameters'].get('signal_threshold', 0.0001)
                    self.config['strategy']['parameters']['signal_threshold'] = new_value
                    updated.append(f"signal_threshold: {old_value} -> {new_value}")
                    logger.info("更新信号阈值", old_value=old_value, new_value=new_value)
                else:
                    errors.append("signal_threshold必须在0.00001-0.01之间")

            # 更新信号过滤器参数
            if 'min_spread' in parameters:
                new_value = float(parameters['min_spread'])
                if 0.0001 <= new_value <= 0.1:
                    if 'strategy' not in self.config:
                        self.config['strategy'] = {}
                    if 'signal_filters' not in self.config['strategy']:
                        self.config['strategy']['signal_filters'] = {}

                    old_value = self.config['strategy']['signal_filters'].get('min_spread', 0.0005)
                    self.config['strategy']['signal_filters']['min_spread'] = new_value
                    updated.append(f"min_spread: {old_value} -> {new_value}")
                    logger.info("更新最小价差过滤", old_value=old_value, new_value=new_value)
                else:
                    errors.append("min_spread必须在0.0001-0.1之间")

            if 'max_spread' in parameters:
                new_value = float(parameters['max_spread'])
                if 0.001 <= new_value <= 1.0:
                    if 'strategy' not in self.config:
                        self.config['strategy'] = {}
                    if 'signal_filters' not in self.config['strategy']:
                        self.config['strategy']['signal_filters'] = {}

                    old_value = self.config['strategy']['signal_filters'].get('max_spread', 0.05)
                    self.config['strategy']['signal_filters']['max_spread'] = new_value
                    updated.append(f"max_spread: {old_value} -> {new_value}")
                    logger.info("更新最大价差过滤", old_value=old_value, new_value=new_value)
                else:
                    errors.append("max_spread必须在0.001-1.0之间")

            if 'min_volume' in parameters:
                new_value = float(parameters['min_volume'])
                if 0.1 <= new_value <= 100.0:
                    if 'strategy' not in self.config:
                        self.config['strategy'] = {}
                    if 'signal_filters' not in self.config['strategy']:
                        self.config['strategy']['signal_filters'] = {}

                    old_value = self.config['strategy']['signal_filters'].get('min_volume', 1.0)
                    self.config['strategy']['signal_filters']['min_volume'] = new_value
                    updated.append(f"min_volume: {old_value} -> {new_value}")
                    logger.info("更新最小成交量过滤", old_value=old_value, new_value=new_value)
                else:
                    errors.append("min_volume必须在0.1-100.0之间")

            return {
                'success': len(errors) == 0,
                'updated': updated,
                'errors': errors,
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error("更新策略参数失败", error=str(e))
            return {
                'success': False,
                'updated': updated,
                'errors': [f"更新失败: {str(e)}"],
                'timestamp': time.time()
            }