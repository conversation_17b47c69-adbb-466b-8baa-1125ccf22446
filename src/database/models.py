"""
数据模型定义

定义交易记录、价格记录、风险指标等数据结构
"""

from dataclasses import dataclass, asdict
from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any
from enum import Enum


class TradeStatus(Enum):
    """交易状态枚举"""
    PENDING = "pending"          # 待执行
    EXECUTED = "executed"        # 已执行
    CANCELLED = "cancelled"      # 已取消
    FAILED = "failed"           # 失败


class TradeType(Enum):
    """交易类型枚举"""
    BUY_ARBITRAGE = "buy_arbitrage"      # 买入套利
    SELL_ARBITRAGE = "sell_arbitrage"    # 卖出套利
    HEDGE = "hedge"                      # 对冲交易


class OrderSide(Enum):
    """订单方向枚举"""
    BUY = "buy"
    SELL = "sell"


@dataclass
class TradeRecord:
    """交易记录"""
    id: str
    timestamp: datetime
    symbol: str
    trade_type: TradeType

    # Binance订单信息
    binance_order_id: Optional[str] = None
    binance_side: Optional[OrderSide] = None
    binance_price: Optional[Decimal] = None
    binance_quantity: Optional[Decimal] = None
    binance_status: Optional[TradeStatus] = None
    binance_fee: Optional[Decimal] = None
    binance_timestamp: Optional[datetime] = None  # Binance订单执行时间

    # Lighter订单信息
    lighter_order_id: Optional[str] = None
    lighter_side: Optional[OrderSide] = None
    lighter_price: Optional[Decimal] = None
    lighter_quantity: Optional[Decimal] = None
    lighter_status: Optional[TradeStatus] = None
    lighter_fee: Optional[Decimal] = None
    lighter_timestamp: Optional[datetime] = None  # Lighter订单执行时间

    # 套利信息
    expected_profit: Optional[Decimal] = None
    actual_profit: Optional[Decimal] = None
    profit_rate: Optional[Decimal] = None
    spread_at_entry: Optional[Decimal] = None
    spread_at_exit: Optional[Decimal] = None

    # 执行信息
    execution_time_ms: Optional[int] = None
    slippage: Optional[Decimal] = None
    status: TradeStatus = TradeStatus.PENDING
    error_message: Optional[str] = None

    # 风险信息
    position_size: Optional[Decimal] = None
    risk_score: Optional[float] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理枚举类型
        for key, value in data.items():
            if isinstance(value, Enum):
                data[key] = value.value
            elif isinstance(value, Decimal):
                data[key] = float(value)
            elif isinstance(value, datetime):
                data[key] = value.isoformat()
        return data


@dataclass
class PriceRecord:
    """价格记录"""
    timestamp: datetime
    symbol: str
    exchange: str

    # 价格信息
    bid_price: Decimal
    ask_price: Decimal
    last_price: Decimal
    volume_24h: Optional[Decimal] = None

    # 深度信息
    bid_quantity: Optional[Decimal] = None
    ask_quantity: Optional[Decimal] = None

    # 统计信息
    high_24h: Optional[Decimal] = None
    low_24h: Optional[Decimal] = None
    change_24h: Optional[Decimal] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        for key, value in data.items():
            if isinstance(value, Decimal):
                data[key] = float(value)
            elif isinstance(value, datetime):
                data[key] = value.isoformat()
        return data


@dataclass
class SpreadRecord:
    """价差记录"""
    timestamp: datetime
    symbol: str

    # 价差信息
    binance_bid: Decimal
    binance_ask: Decimal
    lighter_bid: Decimal
    lighter_ask: Decimal

    # 计算的价差
    bid_spread: Decimal      # binance_bid - lighter_ask
    ask_spread: Decimal      # lighter_bid - binance_ask
    mid_spread: Decimal      # (binance_mid - lighter_mid) / lighter_mid

    # 移动平均
    ma_spread: Optional[Decimal] = None

    # 信号
    signal: Optional[str] = None  # 'buy', 'sell', 'hold'
    signal_strength: Optional[float] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        for key, value in data.items():
            if isinstance(value, Decimal):
                data[key] = float(value)
            elif isinstance(value, datetime):
                data[key] = value.isoformat()
        return data



@dataclass
class SystemStatus:
    """系统状态记录"""
    timestamp: datetime

    # 系统状态
    is_running: bool
    is_trading_enabled: bool
    is_paper_trading: bool

    # 连接状态
    binance_connected: bool
    lighter_connected: bool
    websocket_connected: bool

    # 性能指标
    cpu_usage: float
    memory_usage: float
    network_latency_ms: float

    # 数据状态
    last_price_update: datetime
    last_trade_execution: Optional[datetime] = None

    # 错误信息
    last_error: Optional[str] = None
    error_count: int = 0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
        return data


@dataclass
class PerformanceMetrics:
    """性能指标"""
    timestamp: datetime
    period: str  # 'daily', 'weekly', 'monthly'

    # 交易统计
    total_trades: int
    successful_trades: int
    failed_trades: int
    success_rate: float

    # 盈亏统计
    total_profit: Decimal
    total_loss: Decimal
    net_profit: Decimal
    profit_factor: float  # 总盈利/总亏损

    # 风险调整收益
    sharpe_ratio: float
    max_drawdown: Decimal
    calmar_ratio: float

    # 交易效率
    avg_trade_duration_seconds: float
    avg_profit_per_trade: Decimal
    avg_execution_time_ms: float

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        for key, value in data.items():
            if isinstance(value, Decimal):
                data[key] = float(value)
            elif isinstance(value, datetime):
                data[key] = value.isoformat()
        return data