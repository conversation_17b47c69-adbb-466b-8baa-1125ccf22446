"""
配置文件加载器

负责加载和验证系统配置
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
import structlog

logger = structlog.get_logger(__name__)


class ConfigLoader:
    """配置加载器"""
    
    @staticmethod
    def load_config(config_path: str = "config/settings.yaml") -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置字典
        """
        try:
            config_file = Path(config_path)
            
            if not config_file.exists():
                logger.warning(f"配置文件不存在: {config_path}, 使用默认配置")
                return ConfigLoader._get_default_config()
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 环境变量替换
            config = ConfigLoader._replace_env_variables(config)
            
            logger.info(f"配置文件加载成功: {config_path}")
            return config
            
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            return ConfigLoader._get_default_config()
    
    @staticmethod
    def _replace_env_variables(config: Dict[str, Any]) -> Dict[str, Any]:
        """替换配置中的环境变量"""
        if isinstance(config, dict):
            return {key: ConfigLoader._replace_env_variables(value) for key, value in config.items()}
        elif isinstance(config, list):
            return [ConfigLoader._replace_env_variables(item) for item in config]
        elif isinstance(config, str) and config.startswith('${') and config.endswith('}'):
            env_var = config[2:-1]
            return os.getenv(env_var, config)
        else:
            return config
    
    @staticmethod
    def _get_default_config() -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'trading': {
                'symbol': 'BTC/USDT',
                'max_position_size': 0.1,
                'min_trade_amount': 0.001,
                'max_trade_amount': 0.01,
                'ma_period': 5,
                'min_profit_threshold': 0.001,
                'max_spread_threshold': 0.02,
                'paper_trading': True
            },
            'risk_management': {
                'max_exposure': 1000,
                'position_limit_pct': 0.8,
                'stop_loss_pct': 0.05,
                'max_loss_per_day': 100
            },
            'exchanges': {
                'binance': {
                    'api_key': '',
                    'secret': '',
                    'sandbox': True,
                    'testnet': True
                },
                'lighter': {
                    'api_url': 'https://api.lighter.xyz',
                    'private_key': '',
                    'account_index': 595,
                    'api_key_index': 1
                }
            },
            'database': {
                'path': 'data/arbitrage.db',
                'backup_dir': 'data/backups'
            },
            'web': {
                'host': '127.0.0.1',
                'port': 8000
            },
            'logging': {
                'level': 'INFO',
                'log_file': 'logs/arbitrage.log'
            }
        }
    
    @staticmethod
    def validate_config(config: Dict[str, Any]) -> bool:
        """
        验证配置有效性
        
        Args:
            config: 配置字典
            
        Returns:
            是否有效
        """
        try:
            required_sections = ['trading', 'risk_management', 'exchanges']
            
            for section in required_sections:
                if section not in config:
                    logger.error(f"缺少必要配置段: {section}")
                    return False
            
            # 验证交易配置
            trading_config = config['trading']
            required_trading_keys = ['symbol', 'max_position_size']
            for key in required_trading_keys:
                if key not in trading_config:
                    logger.error(f"缺少交易配置: {key}")
                    return False
            
            # 验证交易所配置
            exchanges_config = config['exchanges']
            required_exchanges = ['binance', 'lighter']
            for exchange in required_exchanges:
                if exchange not in exchanges_config:
                    logger.error(f"缺少交易所配置: {exchange}")
                    return False
            
            logger.info("配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False


# 全局配置实例
config = ConfigLoader() 