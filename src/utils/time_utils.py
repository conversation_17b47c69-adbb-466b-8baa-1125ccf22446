"""
时间工具模块

提供统一的时间处理功能，支持本地时间和UTC时间的转换
"""

from datetime import datetime, timezone
from typing import Optional
import time


class TimeUtils:
    """时间工具类"""
    
    @staticmethod
    def now_local() -> datetime:
        """获取当前本地时间"""
        return datetime.now()
    
    @staticmethod
    def now_utc() -> datetime:
        """获取当前UTC时间"""
        return datetime.utcnow()
    
    @staticmethod
    def utc_to_local(utc_dt: datetime) -> datetime:
        """将UTC时间转换为本地时间"""
        if utc_dt.tzinfo is None:
            # 如果没有时区信息，假设是UTC
            utc_dt = utc_dt.replace(tzinfo=timezone.utc)
        
        # 转换为本地时间
        local_dt = utc_dt.astimezone()
        return local_dt.replace(tzinfo=None)  # 移除时区信息，返回naive datetime
    
    @staticmethod
    def local_to_utc(local_dt: datetime) -> datetime:
        """将本地时间转换为UTC时间"""
        if local_dt.tzinfo is None:
            # 如果没有时区信息，假设是本地时间
            local_dt = local_dt.replace(tzinfo=timezone.utc)
        
        # 转换为UTC时间
        utc_dt = local_dt.astimezone(timezone.utc)
        return utc_dt.replace(tzinfo=None)  # 移除时区信息，返回naive datetime
    
    @staticmethod
    def format_local_time(dt: datetime, include_ms: bool = True) -> str:
        """格式化本地时间为字符串"""
        if dt is None:
            return "--"
        
        # 如果是UTC时间，先转换为本地时间
        if hasattr(dt, 'tzinfo') and dt.tzinfo is not None:
            dt = TimeUtils.utc_to_local(dt)
        
        if include_ms:
            return dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]  # 保留3位毫秒
        else:
            return dt.strftime("%Y-%m-%d %H:%M:%S")
    
    @staticmethod
    def parse_timestamp(timestamp_str: str) -> Optional[datetime]:
        """解析时间戳字符串"""
        if not timestamp_str:
            return None
        
        try:
            # 尝试解析ISO格式
            if 'T' in timestamp_str:
                return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            else:
                # 尝试解析常见格式
                for fmt in [
                    "%Y-%m-%d %H:%M:%S.%f",
                    "%Y-%m-%d %H:%M:%S",
                    "%Y-%m-%d"
                ]:
                    try:
                        return datetime.strptime(timestamp_str, fmt)
                    except ValueError:
                        continue
        except Exception:
            pass
        
        return None
    
    @staticmethod
    def timestamp_to_local(timestamp: float) -> datetime:
        """将Unix时间戳转换为本地时间"""
        return datetime.fromtimestamp(timestamp)
    
    @staticmethod
    def local_to_timestamp(dt: datetime) -> float:
        """将本地时间转换为Unix时间戳"""
        return dt.timestamp()
    
    @staticmethod
    def get_local_timezone_name() -> str:
        """获取本地时区名称"""
        return time.tzname[0]
    
    @staticmethod
    def format_duration_ms(start_time: datetime, end_time: datetime) -> int:
        """计算两个时间之间的毫秒差"""
        if start_time is None or end_time is None:
            return 0
        
        delta = end_time - start_time
        return int(delta.total_seconds() * 1000)
