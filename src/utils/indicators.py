"""
技术指标计算模块

提供套利交易所需的技术指标计算功能
"""

import numpy as np
import pandas as pd
from typing import List, Optional, Tuple, Dict
from collections import deque
import structlog
import time
from datetime import datetime, timezone

logger = structlog.get_logger(__name__)


class MovingAverage:
    """移动平均线计算器"""
    
    def __init__(self, period: int):
        """
        初始化移动平均线计算器
        
        Args:
            period: 计算周期
        """
        self.period = period
        self.values = deque(maxlen=period)
        self._sum = 0.0
    
    def update(self, value: float) -> Optional[float]:
        """
        更新数据并计算移动平均线
        
        Args:
            value: 新的数据点
            
        Returns:
            移动平均线值，如果数据不足则返回None
        """
        if len(self.values) == self.period:
            old_value = self.values[0]
            self._sum -= old_value
        
        self.values.append(value)
        self._sum += value
        
        if len(self.values) == self.period:
            return self._sum / self.period
        
        return None
    
    def get_current(self) -> Optional[float]:
        """获取当前移动平均线值"""
        if len(self.values) == self.period:
            return self._sum / self.period
        return None
    
    def is_ready(self) -> bool:
        """检查是否有足够的数据"""
        return len(self.values) == self.period


class TimeWindowSpreadAnalyzer:
    """基于时间窗口的价差分析器"""

    def __init__(self, window_minutes: int = 1, ma_period: int = 5):
        """
        初始化时间窗口价差分析器

        Args:
            window_minutes: 时间窗口大小（分钟）
            ma_period: 移动平均线周期
        """
        self.window_minutes = window_minutes
        self.window_seconds = window_minutes * 60
        self.ma_period = ma_period

        # 存储时间窗口内的价差数据
        self.window_data = deque()  # [(timestamp, spread_rate), ...]
        self.window_averages = deque(maxlen=ma_period)  # 每个窗口的平均价差
        self.spread_ma = MovingAverage(ma_period)

        # 当前窗口统计
        self.current_window_start = None
        self.current_window_spreads = []

        # 历史数据
        self.spread_history = deque(maxlen=1000)
        self.price_ratios = deque(maxlen=1000)

        logger.info("初始化时间窗口价差分析器",
                   window_minutes=window_minutes, ma_period=ma_period)

    def update_spread(self, binance_price: float, lighter_price: float) -> Dict[str, Optional[float]]:
        """
        更新价差数据并计算时间窗口MA

        Args:
            binance_price: Binance价格
            lighter_price: Lighter价格

        Returns:
            包含各种指标的字典
        """
        if lighter_price <= 0:
            logger.warning("Lighter价格无效", price=lighter_price)
            return {}

        current_time = time.time()

        # 计算价差比例
        diff_rate = (binance_price / lighter_price) - 1

        # 更新历史数据
        self.spread_history.append(diff_rate)
        self.price_ratios.append(binance_price / lighter_price)

        # 初始化当前窗口
        if self.current_window_start is None:
            self.current_window_start = current_time
            self.current_window_spreads = []

        # 检查是否需要开始新窗口
        if current_time - self.current_window_start >= self.window_seconds:
            # 计算当前窗口的平均价差
            if self.current_window_spreads:
                window_avg = sum(self.current_window_spreads) / len(self.current_window_spreads)
                self.window_averages.append(window_avg)

                # 更新MA
                ma_value = self.spread_ma.update(window_avg)

                logger.debug("完成时间窗口",
                           window_avg=window_avg,
                           window_size=len(self.current_window_spreads),
                           ma_value=ma_value)

            # 开始新窗口
            self.current_window_start = current_time
            self.current_window_spreads = [diff_rate]
        else:
            # 添加到当前窗口
            self.current_window_spreads.append(diff_rate)

        # 获取当前MA值
        ma_value = self.spread_ma.get_current()

        # 计算统计指标
        result = {
            'diff_rate': diff_rate,
            'ma': ma_value,
            'std': self._calculate_std() if len(self.spread_history) > 1 else None,
            'zscore': self._calculate_zscore(diff_rate) if ma_value is not None else None,
            'percentile_rank': self._calculate_percentile_rank(diff_rate),
            'window_size': len(self.current_window_spreads),
            'window_progress': (current_time - self.current_window_start) / self.window_seconds * 100
        }

        logger.debug("时间窗口价差指标更新", **result)
        return result

    def _calculate_std(self) -> float:
        """计算标准差"""
        if len(self.spread_history) < 2:
            return 0.0
        return float(np.std(list(self.spread_history)))

    def _calculate_zscore(self, current_spread: float) -> Optional[float]:
        """计算Z分数"""
        if not self.spread_ma.is_ready():
            return None

        std = self._calculate_std()
        if std == 0:
            return 0.0

        ma = self.spread_ma.get_current()
        if ma is None:
            return None

        return (current_spread - ma) / std

    def _calculate_percentile_rank(self, current_spread: float) -> Optional[float]:
        """计算百分位排名"""
        if len(self.spread_history) < 10:
            return None

        spreads = list(self.spread_history)
        rank = sum(1 for x in spreads if x <= current_spread)
        return rank / len(spreads) * 100

    def get_signal(self, current_diff_rate: float, ma_value: Optional[float] = None) -> str:
        """
        获取交易信号

        Args:
            current_diff_rate: 当前价差比例
            ma_value: 移动平均线值

        Returns:
            交易信号: 'buy', 'sell', 'hold'
        """
        if ma_value is None:
            ma_value = self.spread_ma.get_current()

        if ma_value is None:
            return 'hold'

        # 简单的均值回归策略
        if current_diff_rate < ma_value:
            return 'buy'  # binance相对便宜，在binance买入
        elif current_diff_rate > ma_value:
            return 'sell'  # binance相对昂贵，在binance卖出
        else:
            return 'hold'


class SpreadAnalyzer:
    """价差分析器（保持向后兼容）"""

    def __init__(self, ma_period: int = 5, use_time_window: bool = True, window_minutes: int = 1):
        """
        初始化价差分析器

        Args:
            ma_period: 移动平均线周期
            use_time_window: 是否使用时间窗口
            window_minutes: 时间窗口大小（分钟）
        """
        self.ma_period = ma_period
        self.use_time_window = use_time_window

        if use_time_window:
            self.time_analyzer = TimeWindowSpreadAnalyzer(window_minutes, ma_period)
            logger.info("使用时间窗口价差分析", window_minutes=window_minutes)
        else:
            # 传统实时MA
            self.spread_ma = MovingAverage(ma_period)
            logger.info("使用传统实时价差分析")

        self.spread_history = deque(maxlen=1000)
        self.price_ratios = deque(maxlen=1000)
    
    def update_spread(self, binance_price: float, lighter_price: float) -> Dict[str, Optional[float]]:
        """
        更新价差数据并计算指标

        Args:
            binance_price: Binance价格
            lighter_price: Lighter价格

        Returns:
            包含各种指标的字典
        """
        if lighter_price <= 0:
            logger.warning("Lighter价格无效", price=lighter_price)
            return {}

        # 使用时间窗口分析器或传统分析器
        if self.use_time_window:
            result = self.time_analyzer.update_spread(binance_price, lighter_price)
            # 同步历史数据
            self.spread_history = self.time_analyzer.spread_history
            self.price_ratios = self.time_analyzer.price_ratios
            return result
        else:
            # 传统实时MA计算
            diff_rate = (binance_price / lighter_price) - 1

            # 更新历史数据
            self.spread_history.append(diff_rate)
            self.price_ratios.append(binance_price / lighter_price)

            # 计算移动平均线
            ma_value = self.spread_ma.update(diff_rate)

            # 计算统计指标
            result = {
                'diff_rate': diff_rate,
                'ma': ma_value,
                'std': self._calculate_std() if len(self.spread_history) > 1 else None,
                'zscore': self._calculate_zscore(diff_rate) if ma_value is not None else None,
                'percentile_rank': self._calculate_percentile_rank(diff_rate)
            }

            logger.debug("价差指标更新", **result)
            return result
    
    def _calculate_std(self) -> float:
        """计算标准差"""
        if len(self.spread_history) < 2:
            return 0.0
        return float(np.std(list(self.spread_history)))
    
    def _calculate_zscore(self, current_spread: float) -> Optional[float]:
        """计算Z分数"""
        if not self.spread_ma.is_ready():
            return None
        
        std = self._calculate_std()
        if std == 0:
            return 0.0
        
        ma = self.spread_ma.get_current()
        if ma is None:
            return None
        
        return (current_spread - ma) / std
    
    def _calculate_percentile_rank(self, current_spread: float) -> Optional[float]:
        """计算百分位排名"""
        if len(self.spread_history) < 10:
            return None
        
        spreads = list(self.spread_history)
        rank = sum(1 for x in spreads if x <= current_spread)
        return rank / len(spreads) * 100
    
    def get_signal(self, current_diff_rate: float, ma_value: Optional[float] = None) -> str:
        """
        获取交易信号

        Args:
            current_diff_rate: 当前价差比例
            ma_value: 移动平均线值

        Returns:
            交易信号: 'buy', 'sell', 'hold'
        """
        if self.use_time_window:
            return self.time_analyzer.get_signal(current_diff_rate, ma_value)
        else:
            if ma_value is None:
                ma_value = self.spread_ma.get_current()

            if ma_value is None:
                return 'hold'

            # 简单的均值回归策略
            if current_diff_rate < ma_value:
                return 'buy'  # binance相对便宜，在binance买入
            elif current_diff_rate > ma_value:
                return 'sell'  # binance相对昂贵，在binance卖出
            else:
                return 'hold'


class RiskMetrics:
    """风险指标计算器"""
    
    def __init__(self, window: int = 100):
        """
        初始化风险指标计算器
        
        Args:
            window: 计算窗口大小
        """
        self.window = window
        self.returns = deque(maxlen=window)
        self.prices = deque(maxlen=window)
    
    def update(self, price: float) -> Dict[str, Optional[float]]:
        """
        更新价格并计算风险指标
        
        Args:
            price: 新的价格
            
        Returns:
            风险指标字典
        """
        self.prices.append(price)
        
        # 计算收益率
        if len(self.prices) > 1:
            return_rate = (price - self.prices[-2]) / self.prices[-2]
            self.returns.append(return_rate)
        
        return {
            'volatility': self._calculate_volatility(),
            'max_drawdown': self._calculate_max_drawdown(),
            'sharpe_ratio': self._calculate_sharpe_ratio(),
            'var_95': self._calculate_var(0.05)
        }
    
    def _calculate_volatility(self) -> Optional[float]:
        """计算波动率"""
        if len(self.returns) < 2:
            return None
        return float(np.std(list(self.returns)) * np.sqrt(252))  # 年化波动率
    
    def _calculate_max_drawdown(self) -> Optional[float]:
        """计算最大回撤"""
        if len(self.prices) < 2:
            return None
        
        prices = np.array(list(self.prices))
        peak = np.maximum.accumulate(prices)
        drawdown = (prices - peak) / peak
        return float(np.min(drawdown))
    
    def _calculate_sharpe_ratio(self, risk_free_rate: float = 0.02) -> Optional[float]:
        """计算夏普比率"""
        if len(self.returns) < 2:
            return None
        
        returns = np.array(list(self.returns))
        excess_return = np.mean(returns) * 252 - risk_free_rate
        volatility = np.std(returns) * np.sqrt(252)
        
        if volatility == 0:
            return None
        
        return float(excess_return / volatility)
    
    def _calculate_var(self, confidence_level: float) -> Optional[float]:
        """计算风险价值(VaR)"""
        if len(self.returns) < 10:
            return None
        
        returns = np.array(list(self.returns))
        return float(np.percentile(returns, confidence_level * 100))


class PerformanceTracker:
    """性能跟踪器"""
    
    def __init__(self):
        """初始化性能跟踪器"""
        self.trades = []
        self.pnl_history = []
        self.start_time = None
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = 0.0
    
    def record_trade(self, pnl: float, fees: float, timestamp: float) -> None:
        """
        记录交易
        
        Args:
            pnl: 盈亏
            fees: 手续费
            timestamp: 时间戳
        """
        net_pnl = pnl - fees
        
        self.trades.append({
            'pnl': pnl,
            'fees': fees,
            'net_pnl': net_pnl,
            'timestamp': timestamp
        })
        
        self.pnl_history.append(net_pnl)
        self.total_trades += 1
        self.total_pnl += net_pnl
        
        if net_pnl > 0:
            self.winning_trades += 1
    
    def get_statistics(self) -> Dict[str, float]:
        """获取交易统计"""
        if not self.trades:
            return {}
        
        pnls = [trade['net_pnl'] for trade in self.trades]
        
        return {
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'win_rate': self.winning_trades / self.total_trades if self.total_trades > 0 else 0,
            'total_pnl': self.total_pnl,
            'average_pnl': np.mean(pnls),
            'max_profit': np.max(pnls) if pnls else 0,
            'max_loss': np.min(pnls) if pnls else 0,
            'profit_factor': self._calculate_profit_factor(),
            'sharpe_ratio': self._calculate_sharpe() if len(pnls) > 1 else 0
        }
    
    def _calculate_profit_factor(self) -> float:
        """计算盈利因子"""
        if not self.trades:
            return 0
        
        profits = sum(trade['net_pnl'] for trade in self.trades if trade['net_pnl'] > 0)
        losses = abs(sum(trade['net_pnl'] for trade in self.trades if trade['net_pnl'] < 0))
        
        return profits / losses if losses > 0 else float('inf')
    
    def _calculate_sharpe(self) -> float:
        """计算夏普比率"""
        if len(self.pnl_history) < 2:
            return 0
        
        returns = np.array(self.pnl_history)
        return float(np.mean(returns) / np.std(returns)) if np.std(returns) > 0 else 0 