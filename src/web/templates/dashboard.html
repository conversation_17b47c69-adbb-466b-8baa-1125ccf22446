<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binance-Lighter 套利交易监控</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .status-card {
            transition: all 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .price-display {
            font-family: 'Courier New', monospace;
            font-size: 1.2em;
            font-weight: bold;
        }
        .profit-positive {
            color: #28a745;
        }
        .profit-negative {
            color: #dc3545;
        }
        .status-running {
            color: #28a745;
        }
        .status-stopped {
            color: #dc3545;
        }
        .status-warning {
            color: #ffc107;
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
        .alert-item {
            border-left: 4px solid #dc3545;
            margin-bottom: 10px;
        }
        .connection-status {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .trade-item {
            border: 1px solid #e9ecef;
            border-radius: 5px;
            margin-bottom: 8px;
            padding: 10px;
            background-color: #fff;
            transition: all 0.2s ease;
        }
        .trade-item:hover {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .trade-profit-positive {
            border-left: 4px solid #28a745;
        }
        .trade-profit-negative {
            border-left: 4px solid #dc3545;
        }
        .trade-profit-neutral {
            border-left: 4px solid #6c757d;
        }
        .trade-time {
            font-size: 0.8em;
            color: #6c757d;
        }
        .trade-amount {
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 右上角操作区域 -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 1050;">
        <div class="d-flex align-items-center gap-2">
            <!-- 连接状态 -->
            <span id="connectionStatus" class="badge bg-secondary">
                <i class="fas fa-circle"></i> 连接中...
            </span>

            <!-- 操作按钮组 -->
            <div class="btn-group" role="group">
                <button id="emergencyStopBtn" class="btn btn-danger btn-sm" title="紧急停止">
                    <i class="fas fa-stop"></i>
                </button>
                <button id="resetEmergencyBtn" class="btn btn-warning btn-sm" disabled title="重置停止">
                    <i class="fas fa-redo"></i>
                </button>
                <button id="resetTradingDataBtn" class="btn btn-info btn-sm" data-bs-toggle="modal" data-bs-target="#resetDataModal" data-reset-type="trading" title="重置交易数据">
                    <i class="fas fa-refresh"></i>
                </button>
                <button id="resetAllDataBtn" class="btn btn-secondary btn-sm" data-bs-toggle="modal" data-bs-target="#resetDataModal" data-reset-type="all" title="重置全部数据">
                    <i class="fas fa-trash-restore"></i>
                </button>
            </div>
        </div>
    </div>

    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 mb-0">
                    <i class="fas fa-chart-line text-primary"></i>
                    Binance-Lighter 套利交易监控
                </h1>
                <p class="text-muted">实时监控套利交易系统状态和性能</p>
            </div>
        </div>

        <!-- 系统状态卡片 -->
        <div class="row mb-4">
            <div class="col-md-2 col-sm-6 mb-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-power-off fa-2x mb-2" id="systemStatusIcon"></i>
                        <h5 class="card-title">系统状态</h5>
                        <p class="card-text" id="systemStatus">
                            <span class="loading-spinner"></span> 加载中...
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-2 col-sm-6 mb-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-wallet fa-2x mb-2 text-primary"></i>
                        <h5 class="card-title">总资产</h5>
                        <p class="card-text price-display" id="totalAssets">
                            <span class="loading-spinner"></span>
                        </p>
                        <small class="text-muted" id="assetsDetail" style="font-size: 0.7em;">
                            <div>详情加载中...</div>
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-2 col-sm-6 mb-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-dollar-sign fa-2x mb-2 text-success"></i>
                        <h5 class="card-title">总盈亏</h5>
                        <p class="card-text price-display" id="totalProfit">
                            <span class="loading-spinner"></span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-2 col-sm-6 mb-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-exchange-alt fa-2x mb-2 text-info"></i>
                        <h5 class="card-title">交易次数</h5>
                        <p class="card-text" id="totalTrades">
                            <span class="loading-spinner"></span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-2 col-sm-6 mb-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-percentage fa-2x mb-2 text-warning"></i>
                        <h5 class="card-title">成功率</h5>
                        <p class="card-text" id="successRate">
                            <span class="loading-spinner"></span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-2 col-sm-6 mb-3">
                <div class="card status-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-bar fa-2x mb-2 text-info"></i>
                        <h5 class="card-title">BTC价格</h5>
                        <p class="card-text price-display" id="btcPrice">
                            <span class="loading-spinner"></span>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 价格和信号 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-coins"></i> 实时价格
                        </h5>
                    </div>
                    <div class="card-body" style="height: 350px;">
                        <div class="row">
                            <div class="col-6">
                                <h6>Binance</h6>
                                <div class="price-display text-primary">
                                    买一: <span id="binanceBid">--</span><br>
                                    卖一: <span id="binanceAsk">--</span><br>
                                    最新: <span id="binanceLast">--</span>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6>Lighter</h6>
                                <div class="price-display text-success">
                                    买一: <span id="lighterBid">--</span><br>
                                    卖一: <span id="lighterAsk">--</span><br>
                                    最新: <span id="lighterLast">--</span>
                                    <small id="lighterLastSource" class="text-muted d-block" style="font-size: 0.7em;"></small>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="text-center">
                            <h6>价差MA (5分钟): <span id="spreadMA" class="price-display">--</span></h6>
                            <div id="currentSignal" class="badge bg-secondary">无信号</div>
                        </div>
                        <hr>
                        <!-- 价差MA计算公式 -->
                        <div class="mt-3">
                            <h6 class="text-muted mb-2">价差MA计算:</h6>
                            <div class="formula-container p-2 bg-light rounded" style="font-size: 0.85em;">
                                <div class="mb-1">
                                    <strong>当前价差率:</strong> <span id="currentSpreadRate" class="text-primary">--</span>
                                </div>
                                <div class="mb-1">
                                    <strong>时间窗口:</strong> <span id="timeWindowDisplay" class="text-info">1分钟</span> |
                                    <strong>MA周期:</strong> <span id="maPeriodDisplay" class="text-info">--</span>
                                </div>
                                <div class="mb-1">
                                    <strong>窗口进度:</strong> <span id="windowProgress" class="text-warning">--</span>
                                </div>
                                <div class="mb-1">
                                    <strong>计算方式:</strong> <code>每5分钟计算一次窗口平均价差，然后对窗口平均值计算MA</code>
                                </div>
                                <div>
                                    <strong>价差率 =</strong> <code>(Lighter价格 - Binance价格) / Binance价格</code>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-area"></i> 价差走势
                        </h5>
                    </div>
                    <div class="card-body" style="height: 350px;">
                        <div class="chart-container" style="height: 100%;">
                            <canvas id="spreadChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 交易记录 -->
        <div class="row mb-4">
            <!-- 当前挂单 -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-clock"></i> 当前挂单
                        </h5>
                        <button id="refreshOrdersBtn" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="ordersContainer" style="height: 300px; overflow-y: auto;">
                            <div class="text-center text-muted">
                                <div class="loading-spinner"></div>
                                <div>加载挂单信息...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 成交记录 -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-history"></i> 成交记录
                        </h5>
                        <button id="refreshTradesBtn" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="tradesContainer" style="height: 300px; overflow-y: auto;">
                            <div class="text-center text-muted">
                                <div class="loading-spinner"></div>
                                <div>加载成交记录...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 交易所余额和持仓信息 -->
        <div class="row mb-4">
            <!-- Binance 余额和持仓 -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-coins text-warning"></i> Binance 账户信息
                        </h5>
                        <button id="refreshBinanceBtn" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body">
                        <!-- 余额信息 -->
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-wallet"></i> 保证金余额
                            </h6>
                            <div class="row">
                                <div class="col-12">
                                    <div class="text-center p-3 bg-light rounded">
                                        <div class="text-muted small">USDT 保证金</div>
                                        <div class="price-display text-primary fs-5" id="binanceUsdtBalance">
                                            <span class="loading-spinner"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6" style="display: none;">
                                    <div class="text-center p-2 bg-light rounded">
                                        <div class="text-muted small">BTC 余额</div>
                                        <div class="price-display text-warning" id="binanceBtcBalance">
                                            <span class="loading-spinner"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 持仓信息 -->
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-chart-pie"></i> 持仓信息
                            </h6>
                            <div id="binancePositions" style="max-height: 200px; overflow-y: auto;">
                                <div class="text-center text-muted">
                                    <div class="loading-spinner"></div>
                                    <div>加载持仓信息...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Lighter 余额和持仓 -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-coins text-success"></i> Lighter 账户信息
                        </h5>
                        <button id="refreshLighterBtn" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body">
                        <!-- 余额信息 -->
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-wallet"></i> 保证金余额
                            </h6>
                            <div class="row">
                                <div class="col-12">
                                    <div class="text-center p-3 bg-light rounded">
                                        <div class="text-muted small">USDT 保证金</div>
                                        <div class="price-display text-primary fs-5" id="lighterUsdtBalance">
                                            <span class="loading-spinner"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6" style="display: none;">
                                    <div class="text-center p-2 bg-light rounded">
                                        <div class="text-muted small">BTC 余额</div>
                                        <div class="price-display text-warning" id="lighterBtcBalance">
                                            <span class="loading-spinner"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 持仓信息 -->
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-chart-pie"></i> 持仓信息
                            </h6>
                            <div id="lighterPositions" style="max-height: 200px; overflow-y: auto;">
                                <div class="text-center text-muted">
                                    <div class="loading-spinner"></div>
                                    <div>加载持仓信息...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 策略参数 -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs"></i> 策略参数
                        </h5>
                        <button id="editStrategyBtn" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i> 编辑参数
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row" id="strategyParametersView">
                            <div class="col-md-4">
                                <h6>基础参数</h6>
                                <ul class="list-unstyled">
                                    <li>交易对: <span id="paramSymbol" class="fw-bold">--</span></li>
                                    <li>MA周期: <span id="paramMaPeriod" class="fw-bold">--</span></li>
                                    <li>最小盈利阈值: <span id="paramMinProfit" class="fw-bold">--</span></li>
                                    <li>最大价差阈值: <span id="paramMaxSpread" class="fw-bold">--</span></li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6>交易量参数</h6>
                                <ul class="list-unstyled">
                                    <li>最大交易量: <span id="paramMaxAmount" class="fw-bold">--</span></li>
                                    <li>最小交易量: <span id="paramMinAmount" class="fw-bold">--</span></li>
                                    <li>冷却时间: <span id="paramCooldown" class="fw-bold">--</span> 秒</li>
                                    <li>信号阈值: <span id="paramSignalThreshold" class="fw-bold">--</span></li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6>过滤参数</h6>
                                <ul class="list-unstyled">
                                    <li>最小价差过滤: <span id="paramMinSpreadFilter" class="fw-bold">--</span></li>
                                    <li>最大价差过滤: <span id="paramMaxSpreadFilter" class="fw-bold">--</span></li>
                                    <li>最小成交量: <span id="paramMinVolume" class="fw-bold">--</span></li>
                                </ul>
                            </div>
                        </div>

                        <!-- 编辑表单（默认隐藏） -->
                        <div id="strategyParametersEdit" style="display: none;">
                            <form id="strategyParametersForm">
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6>基础参数</h6>
                                        <div class="mb-3">
                                            <label for="editMaPeriod" class="form-label">MA周期 (5-100)</label>
                                            <input type="number" class="form-control" id="editMaPeriod" min="5" max="100" step="1">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editMinProfit" class="form-label">最小盈利阈值 (0.0001-0.1)</label>
                                            <input type="number" class="form-control" id="editMinProfit" min="0.0001" max="0.1" step="0.0001">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editMaxSpread" class="form-label">最大价差阈值 (0.001-0.5)</label>
                                            <input type="number" class="form-control" id="editMaxSpread" min="0.001" max="0.5" step="0.001">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>交易量参数</h6>
                                        <div class="mb-3">
                                            <label for="editMaxAmount" class="form-label">最大交易量 (0.001-10.0)</label>
                                            <input type="number" class="form-control" id="editMaxAmount" min="0.001" max="10.0" step="0.001">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editMinAmount" class="form-label">最小交易量 (0.0001-1.0)</label>
                                            <input type="number" class="form-control" id="editMinAmount" min="0.0001" max="1.0" step="0.0001">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editCooldown" class="form-label">冷却时间 (1-300秒)</label>
                                            <input type="number" class="form-control" id="editCooldown" min="1" max="300" step="1">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editSignalThreshold" class="form-label">信号阈值 (0.00001-0.01)</label>
                                            <input type="number" class="form-control" id="editSignalThreshold" min="0.00001" max="0.01" step="0.00001">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>过滤参数</h6>
                                        <div class="mb-3">
                                            <label for="editMinSpreadFilter" class="form-label">最小价差过滤 (0.0001-0.1)</label>
                                            <input type="number" class="form-control" id="editMinSpreadFilter" min="0.0001" max="0.1" step="0.0001">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editMaxSpreadFilter" class="form-label">最大价差过滤 (0.001-1.0)</label>
                                            <input type="number" class="form-control" id="editMaxSpreadFilter" min="0.001" max="1.0" step="0.001">
                                        </div>
                                        <div class="mb-3">
                                            <label for="editMinVolume" class="form-label">最小成交量 (0.1-100.0)</label>
                                            <input type="number" class="form-control" id="editMinVolume" min="0.1" max="100.0" step="0.1">
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <button type="button" id="cancelEditBtn" class="btn btn-secondary me-2">取消</button>
                                    <button type="submit" id="saveParametersBtn" class="btn btn-primary">保存参数</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- 系统日志 -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> 系统日志
                        </h5>
                        <button id="clearLogsBtn" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-trash"></i> 清空
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="systemLogs" style="height: 300px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 0.9em;">
                            <div class="text-muted">等待日志数据...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据重置模态框 -->
    <div class="modal fade" id="resetDataModal" tabindex="-1" aria-labelledby="resetDataModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="resetDataModalLabel">
                        <i class="fas fa-database"></i> 数据重置确认
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="resetDataContent">
                        <!-- 重置类型说明 -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> 重置说明</h6>
                            <div id="resetTypeDescription">
                                <!-- 动态内容 -->
                            </div>
                        </div>

                        <!-- 当前数据统计 -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">当前数据统计</h6>
                            </div>
                            <div class="card-body">
                                <div class="row" id="currentDataStats">
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li>交易记录: <span id="statsTradesCount" class="fw-bold">--</span></li>
                                            <li>价格记录: <span id="statsPricesCount" class="fw-bold">--</span></li>
                                            <li>价差记录: <span id="statsSpreadsCount" class="fw-bold">--</span></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li>系统状态: <span id="statsSystemCount" class="fw-bold">--</span></li>
                                            <li>性能指标: <span id="statsPerformanceCount" class="fw-bold">--</span></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="text-center mt-2">
                                    <strong>总记录数: <span id="statsTotalCount" class="text-primary">--</span></strong>
                                </div>
                            </div>
                        </div>

                        <!-- 备份选项 -->
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="createBackupCheck" checked>
                            <label class="form-check-label" for="createBackupCheck">
                                <i class="fas fa-save"></i> 重置前创建数据备份
                            </label>
                            <small class="form-text text-muted d-block">
                                建议保留备份以防需要恢复数据
                            </small>
                        </div>

                        <!-- 最近备份 -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">最近备份文件</h6>
                            </div>
                            <div class="card-body">
                                <div id="recentBackups" class="small">
                                    <div class="text-muted">加载中...</div>
                                </div>
                            </div>
                        </div>

                        <!-- 警告信息 -->
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> 重要提醒</h6>
                            <ul class="mb-0">
                                <li>此操作将永久删除选定的数据，无法撤销</li>
                                <li>重置过程中系统将暂停交易</li>
                                <li>建议在系统空闲时进行重置操作</li>
                                <li>重置完成后统计数据将归零</li>
                            </ul>
                        </div>
                    </div>

                    <!-- 重置进度 -->
                    <div id="resetProgress" style="display: none;">
                        <div class="text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">重置中...</span>
                            </div>
                            <h5>正在重置数据...</h5>
                            <p class="text-muted">请稍候，不要关闭页面</p>
                        </div>
                    </div>

                    <!-- 重置结果 -->
                    <div id="resetResult" style="display: none;">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle"></i> 重置完成</h6>
                            <div id="resetResultMessage">
                                <!-- 动态内容 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="resetCancelBtn">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmResetBtn">
                        <i class="fas fa-trash-restore"></i> 确认重置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // WebSocket连接
        let ws = null;
        let reconnectInterval = null;
        let spreadChart = null;
        let spreadData = [];
        let maxDataPoints = 50;

        // 初始化图表
        function initChart() {
            const ctx = document.getElementById('spreadChart').getContext('2d');
            spreadChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '价差比例',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1
                    }, {
                        label: '移动平均',
                        data: [],
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false
                        }
                    },
                    plugins: {
                        legend: {
                            display: true
                        }
                    }
                }
            });
        }

        // 连接WebSocket
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;

            ws = new WebSocket(wsUrl);

            ws.onopen = function(event) {
                console.log('WebSocket连接已建立');
                updateConnectionStatus('connected');

                // 订阅实时数据
                ws.send(JSON.stringify({type: 'subscribe'}));

                // 清除重连定时器
                if (reconnectInterval) {
                    clearInterval(reconnectInterval);
                    reconnectInterval = null;
                }

                // 显示连接成功通知
                showNotification('实时数据连接已建立', 'success');
            };

            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                } catch (e) {
                    console.error('解析WebSocket消息失败:', e);
                }
            };

            ws.onclose = function(event) {
                console.log('WebSocket连接已关闭');
                updateConnectionStatus('disconnected');

                // 尝试重连
                if (!reconnectInterval) {
                    reconnectInterval = setInterval(() => {
                        console.log('尝试重新连接WebSocket...');
                        connectWebSocket();
                    }, 3000); // 每3秒重连一次
                }
            };

            ws.onerror = function(error) {
                console.error('WebSocket错误:', error);
                updateConnectionStatus('error');
            };
        }

        // 更新连接状态
        function updateConnectionStatus(status) {
            const statusElement = document.getElementById('connectionStatus');
            switch (status) {
                case 'connected':
                    statusElement.className = 'badge bg-success';
                    statusElement.innerHTML = '<i class="fas fa-circle"></i> 已连接';
                    break;
                case 'disconnected':
                    statusElement.className = 'badge bg-warning';
                    statusElement.innerHTML = '<i class="fas fa-circle"></i> 重连中...';
                    break;
                case 'error':
                    statusElement.className = 'badge bg-danger';
                    statusElement.innerHTML = '<i class="fas fa-circle"></i> 连接错误';
                    break;
                default:
                    statusElement.className = 'badge bg-secondary';
                    statusElement.innerHTML = '<i class="fas fa-circle"></i> 连接中...';
            }
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(data) {
            switch (data.type) {
                case 'price_update':
                    updatePricesOnly(data.data);
                    break;
                case 'status_update':
                    updateSystemStatus(data.data);
                    break;
                case 'connection_status':
                    updateConnectionInfo(data.data);
                    break;
                case 'log_update':
                    updateSystemLogs(data.data);
                    break;
                case 'trade_update':
                    handleTradeUpdate(data.data);
                    break;
                case 'strategy_parameters_updated':
                    handleStrategyParametersUpdate(data.data);
                    break;
                case 'emergency_stop':
                case 'emergency_reset':
                    showNotification(data.message, data.type === 'emergency_stop' ? 'danger' : 'success');
                    break;
                case 'subscription_confirmed':
                    console.log('订阅确认:', data.message);
                    break;
                case 'pong':
                    // 心跳响应
                    break;
                default:
                    console.log('未知消息类型:', data);
            }
        }

        // 高频更新价格数据（新增）
        function updatePricesOnly(priceData) {
            const binancePrices = priceData.binance_prices || {};
            const lighterPrices = priceData.lighter_prices || {};

            // 更新Binance价格
            if (binancePrices.bid) document.getElementById('binanceBid').textContent = binancePrices.bid;
            if (binancePrices.ask) document.getElementById('binanceAsk').textContent = binancePrices.ask;
            if (binancePrices.last) document.getElementById('binanceLast').textContent = binancePrices.last;

            // 更新Lighter价格
            if (lighterPrices.bid) document.getElementById('lighterBid').textContent = lighterPrices.bid;
            if (lighterPrices.ask) document.getElementById('lighterAsk').textContent = lighterPrices.ask;
            if (lighterPrices.last) document.getElementById('lighterLast').textContent = lighterPrices.last;

            // 更新Lighter价格来源信息
            const lighterLastElement = document.getElementById('lighterLast');
            const lighterSourceElement = document.getElementById('lighterLastSource');

            if (lighterPrices.last && lighterPrices.last !== '--') {
                // 判断是否为买卖价中间价
                const bid = parseFloat(lighterPrices.bid);
                const ask = parseFloat(lighterPrices.ask);
                const last = parseFloat(lighterPrices.last);

                if (bid && ask && Math.abs(last - (bid + ask) / 2) < 0.01) {
                    lighterSourceElement.textContent = '(买卖价中间价)';
                    lighterSourceElement.className = 'text-warning d-block';
                } else {
                    lighterSourceElement.textContent = '(真实成交价)';
                    lighterSourceElement.className = 'text-success d-block';
                }
            } else {
                lighterSourceElement.textContent = '';
            }

            // 更新价差MA
            const spreadMA = priceData.spread_ma_value;
            if (spreadMA !== undefined) {
                document.getElementById('spreadMA').textContent = spreadMA ? spreadMA.toFixed(6) : '--';
            }

            // 更新价差MA计算公式显示
            updateSpreadMAFormula(binancePrices, lighterPrices, priceData);

            // 更新当前信号
            const currentSignal = priceData.current_signal;
            const signalElement = document.getElementById('currentSignal');
            if (currentSignal) {
                const signalType = currentSignal.signal_type;
                let badgeClass = 'bg-secondary';
                let signalText = '无信号';

                if (signalType === 'buy') {
                    badgeClass = 'bg-success';
                    signalText = '买入信号';
                } else if (signalType === 'sell') {
                    badgeClass = 'bg-danger';
                    signalText = '卖出信号';
                }

                signalElement.className = `badge ${badgeClass}`;
                signalElement.textContent = signalText;
            }

            // 更新价差图表
            updateSpreadChart(binancePrices.last, lighterPrices.last, spreadMA);
        }

        // 更新价差MA计算公式显示
        function updateSpreadMAFormula(binancePrices, lighterPrices, priceData) {
            const binancePrice = parseFloat(binancePrices.last);
            const lighterPrice = parseFloat(lighterPrices.last);

            if (binancePrice && lighterPrice) {
                // 计算当前价差率
                const currentSpreadRate = (lighterPrice - binancePrice) / binancePrice;
                const spreadRatePercent = (currentSpreadRate * 100).toFixed(4);
                const spreadRateText = currentSpreadRate >= 0 ? `+${spreadRatePercent}%` : `${spreadRatePercent}%`;
                document.getElementById('currentSpreadRate').textContent = spreadRateText;

                // 根据正负值设置颜色
                const spreadElement = document.getElementById('currentSpreadRate');
                if (currentSpreadRate > 0) {
                    spreadElement.className = 'text-success'; // 绿色表示正值
                } else if (currentSpreadRate < 0) {
                    spreadElement.className = 'text-danger'; // 红色表示负值
                } else {
                    spreadElement.className = 'text-primary'; // 蓝色表示零值
                }
            } else {
                document.getElementById('currentSpreadRate').textContent = '--';
                document.getElementById('currentSpreadRate').className = 'text-primary';
            }

            // 更新MA周期显示
            const maPeriod = priceData.ma_period || '--';
            document.getElementById('maPeriodDisplay').textContent = maPeriod;

            // 更新时间窗口信息
            if (priceData.window_progress !== undefined && priceData.window_progress !== null) {
                document.getElementById('windowProgress').textContent =
                    `${priceData.window_progress.toFixed(1)}%`;
            } else {
                document.getElementById('windowProgress').textContent = '--';
            }

            // 更新时间窗口显示
            if (priceData.window_minutes !== undefined && priceData.window_minutes !== null) {
                document.getElementById('timeWindowDisplay').textContent =
                    `${priceData.window_minutes}分钟`;
            } else {
                document.getElementById('timeWindowDisplay').textContent = '--';
            }
        }

        // 更新连接状态信息（新增）
        function updateConnectionInfo(connectionData) {
            // 更新连接状态指示器
            const statusElement = document.getElementById('connectionStatus');
            const allConnected = connectionData.binance_connected &&
                                connectionData.lighter_connected &&
                                connectionData.websocket_connected;

            if (allConnected && connectionData.is_running) {
                statusElement.className = 'badge bg-success';
                statusElement.innerHTML = '<i class="fas fa-circle"></i> 全部连接';
            } else if (connectionData.is_running) {
                statusElement.className = 'badge bg-warning';
                statusElement.innerHTML = '<i class="fas fa-circle"></i> 部分连接';
            } else {
                statusElement.className = 'badge bg-danger';
                statusElement.innerHTML = '<i class="fas fa-circle"></i> 已停止';
            }
        }

        // 更新系统状态
        function updateSystemStatus(status) {
            // 更新系统运行状态
            const systemStatusElement = document.getElementById('systemStatus');
            const systemStatusIcon = document.getElementById('systemStatusIcon');

            if (status.is_running) {
                systemStatusElement.innerHTML = '<span class="status-running">运行中</span>';
                systemStatusIcon.className = 'fas fa-power-off fa-2x mb-2 text-success';
            } else {
                systemStatusElement.innerHTML = '<span class="status-stopped">已停止</span>';
                systemStatusIcon.className = 'fas fa-power-off fa-2x mb-2 text-danger';
            }

            // 更新统计信息
            const stats = status.stats || {};
            document.getElementById('totalProfit').innerHTML =
                `<span class="${stats.total_profit >= 0 ? 'profit-positive' : 'profit-negative'}">
                    ${(stats.total_profit || 0).toFixed(4)} USDT
                </span>`;

            document.getElementById('totalTrades').textContent = stats.total_trades || 0;

            const successRate = stats.total_trades > 0 ?
                ((stats.successful_trades || 0) / stats.total_trades * 100).toFixed(1) : 0;
            document.getElementById('successRate').textContent = `${successRate}%`;

            // 加载总资产信息
            loadTotalAssets();

            // 更新紧急停止按钮状态（简化版，不依赖风险管理）
            updateEmergencyButtons(false); // 默认为false，因为风险管理模块已移除
        }



        // 更新紧急停止按钮
        function updateEmergencyButtons(isEmergencyStop) {
            const emergencyBtn = document.getElementById('emergencyStopBtn');
            const resetBtn = document.getElementById('resetEmergencyBtn');

            if (isEmergencyStop) {
                emergencyBtn.disabled = true;
                emergencyBtn.innerHTML = '<i class="fas fa-stop"></i> 已停止';
                resetBtn.disabled = false;
            } else {
                emergencyBtn.disabled = false;
                emergencyBtn.innerHTML = '<i class="fas fa-stop"></i> 紧急停止';
                resetBtn.disabled = true;
            }
        }

        // 更新价差图表
        function updateSpreadChart(binancePrice, lighterPrice, spreadMA) {
            if (!binancePrice || !lighterPrice || !spreadChart) return;

            const diffRate = (binancePrice / lighterPrice) - 1;
            const now = new Date().toLocaleTimeString();

            // 添加新数据点
            spreadChart.data.labels.push(now);
            spreadChart.data.datasets[0].data.push(diffRate);
            spreadChart.data.datasets[1].data.push(spreadMA);

            // 限制数据点数量
            if (spreadChart.data.labels.length > maxDataPoints) {
                spreadChart.data.labels.shift();
                spreadChart.data.datasets[0].data.shift();
                spreadChart.data.datasets[1].data.shift();
            }

            spreadChart.update('none');
        }

        // 处理交易更新（新增）
        function handleTradeUpdate(tradeData) {
            console.log('收到交易更新:', tradeData);

            // 显示交易通知
            const profit = parseFloat(tradeData.actual_profit || 0);
            const profitText = profit >= 0 ? `+${profit.toFixed(4)}` : profit.toFixed(4);
            const notificationType = profit >= 0 ? 'success' : 'warning';

            showNotification(
                `新交易完成: ${profitText} USDT`,
                notificationType
            );

            // 立即刷新交易记录
            loadTradeRecords();
        }

        // 处理策略参数更新
        function handleStrategyParametersUpdate(updateData) {
            console.log('收到策略参数更新:', updateData);

            // 显示更新通知
            if (updateData.updated && updateData.updated.length > 0) {
                showNotification(
                    `策略参数已更新: ${updateData.updated.join(', ')}`,
                    'success'
                );
            }

            // 重新加载策略参数
            loadStrategyParameters();
        }

        // 加载总资产信息
        async function loadTotalAssets() {
            try {
                const response = await fetch('/api/assets');
                const data = await response.json();

                if (data.assets) {
                    updateTotalAssetsDisplay(data.assets);
                } else {
                    console.error('加载总资产失败:', data.error);
                }
            } catch (error) {
                console.error('加载总资产失败:', error);
                // 显示错误状态
                document.getElementById('totalAssets').innerHTML = '<span class="text-danger">加载失败</span>';
                document.getElementById('assetsDetail').innerHTML = '<div class="text-danger">无法获取资产信息</div>';
                document.getElementById('btcPrice').innerHTML = '<span class="text-danger">--</span>';
            }
        }

        // 更新总资产显示
        function updateTotalAssetsDisplay(assets) {
            // 更新总资产USDT价值
            const totalValue = assets.total_usdt_value || 0;
            document.getElementById('totalAssets').innerHTML =
                `<span class="text-primary fw-bold">${totalValue.toFixed(2)} USDT</span>`;

            // 更新BTC价格
            const btcPrice = assets.btc_price || 0;
            document.getElementById('btcPrice').innerHTML =
                `<span class="text-info fw-bold">${btcPrice.toFixed(2)} USDT</span>`;

            // 更新资产详情
            const binanceUsdt = assets.binance_usdt || 0;
            const binanceBtc = assets.binance_btc || 0;
            const lighterUsdt = assets.lighter_usdt || 0;
            const lighterBtc = assets.lighter_btc || 0;

            const detailHtml = `
                <div style="font-size: 0.65em; line-height: 1.2;">
                    <div class="mb-1">
                        <strong>Binance:</strong><br>
                        ${binanceUsdt.toFixed(2)} USDT<br>
                        ${binanceBtc.toFixed(6)} BTC
                    </div>
                    <div>
                        <strong>Lighter:</strong><br>
                        ${lighterUsdt.toFixed(2)} USDT<br>
                        ${lighterBtc.toFixed(6)} BTC
                    </div>
                </div>
            `;

            document.getElementById('assetsDetail').innerHTML = detailHtml;

            console.log('总资产更新完成:', {
                total_usdt_value: totalValue,
                btc_price: btcPrice,
                binance_usdt: binanceUsdt,
                binance_btc: binanceBtc,
                lighter_usdt: lighterUsdt,
                lighter_btc: lighterBtc
            });

            // 同时更新详细的余额和持仓信息
            updateExchangeBalances(assets);
        }

        // 更新交易所余额和持仓信息
        function updateExchangeBalances(assets) {
            // 更新Binance余额（合约交易只显示USDT保证金）
            const binanceUsdt = assets.binance_usdt || 0;
            document.getElementById('binanceUsdtBalance').innerHTML =
                `<span class="fw-bold">${binanceUsdt.toFixed(2)}</span>`;

            // 合约交易不显示BTC余额，隐藏BTC余额显示
            const binanceBtcElement = document.getElementById('binanceBtcBalance');
            if (binanceBtcElement) {
                binanceBtcElement.closest('.col-6').style.display = 'none';
            }

            // 更新Lighter余额（合约交易只显示USDT保证金）
            const lighterUsdt = assets.lighter_usdt || 0;
            document.getElementById('lighterUsdtBalance').innerHTML =
                `<span class="fw-bold">${lighterUsdt.toFixed(2)}</span>`;

            // 合约交易不显示BTC余额，隐藏BTC余额显示
            const lighterBtcElement = document.getElementById('lighterBtcBalance');
            if (lighterBtcElement) {
                lighterBtcElement.closest('.col-6').style.display = 'none';
            }

            // 初始化时显示空持仓（项目启动时持仓为0）
            updatePositionsDisplay('binance', {});
            updatePositionsDisplay('lighter', {});
        }

        // 更新持仓显示（合约交易）
        function updatePositionsDisplay(exchange, positions) {
            const containerId = exchange + 'Positions';
            const container = document.getElementById(containerId);

            if (!positions || Object.keys(positions).length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted p-3">
                        <i class="fas fa-chart-line mb-2"></i>
                        <div>暂无持仓</div>
                        <small>项目启动时持仓总和为零</small>
                    </div>
                `;
                return;
            }

            const positionsHtml = Object.entries(positions)
                .filter(([symbol, position]) => {
                    // 过滤掉没有持仓的合约
                    return position.size && Math.abs(parseFloat(position.size)) > 0.0001;
                })
                .map(([symbol, position]) => {
                    const size = parseFloat(position.size || 0);
                    const side = position.side || 'none';
                    const unrealizedPnl = parseFloat(position.unrealized_pnl || 0);
                    const entryPrice = parseFloat(position.entry_price || 0);

                    // 确定持仓方向的颜色和图标
                    let sideClass = 'text-muted';
                    let sideIcon = 'fas fa-minus';
                    let sideText = '无持仓';

                    if (side === 'long') {
                        sideClass = 'text-success';
                        sideIcon = 'fas fa-arrow-up';
                        sideText = '多头';
                    } else if (side === 'short') {
                        sideClass = 'text-danger';
                        sideIcon = 'fas fa-arrow-down';
                        sideText = '空头';
                    }

                    // 盈亏颜色
                    const pnlClass = unrealizedPnl >= 0 ? 'text-success' : 'text-danger';

                    return `
                        <div class="border rounded p-2 mb-2">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <div class="fw-bold">${symbol}</div>
                                <div class="${sideClass}">
                                    <i class="${sideIcon}"></i> ${sideText}
                                </div>
                            </div>
                            <div class="row small">
                                <div class="col-6">
                                    <div class="text-muted">数量</div>
                                    <div class="fw-bold">${Math.abs(size).toFixed(4)}</div>
                                </div>
                                <div class="col-6">
                                    <div class="text-muted">未实现盈亏</div>
                                    <div class="fw-bold ${pnlClass}">${unrealizedPnl.toFixed(2)} USDT</div>
                                </div>
                            </div>
                            ${entryPrice > 0 ? `
                                <div class="mt-1 small text-muted">
                                    开仓价: ${entryPrice.toFixed(2)}
                                </div>
                            ` : ''}
                        </div>
                    `;
                }).join('');

            container.innerHTML = positionsHtml || `
                <div class="text-center text-muted p-3">
                    <i class="fas fa-chart-line mb-2"></i>
                    <div>暂无持仓</div>
                    <small>项目启动时持仓总和为零</small>
                </div>
            `;
        }

        // 加载详细的交易所账户信息
        async function loadExchangeAccountInfo(exchange) {
            try {
                const response = await fetch(`/api/account/${exchange}`);
                const data = await response.json();

                if (data.success) {
                    updateExchangeAccountDisplay(exchange, data.account_info);
                } else {
                    console.error(`加载${exchange}账户信息失败:`, data.error);
                }
            } catch (error) {
                console.error(`加载${exchange}账户信息失败:`, error);
            }
        }

        // 更新交易所账户显示
        function updateExchangeAccountDisplay(exchange, accountInfo) {
            if (!accountInfo) return;

            // 更新余额
            if (accountInfo.balances) {
                Object.entries(accountInfo.balances).forEach(([asset, balance]) => {
                    const elementId = `${exchange}${asset}Balance`;
                    const element = document.getElementById(elementId);
                    if (element) {
                        const amount = parseFloat(balance.free || balance.available || balance);
                        const displayAmount = asset === 'BTC' ? amount.toFixed(6) : amount.toFixed(2);
                        element.innerHTML = `<span class="fw-bold">${displayAmount}</span>`;
                    }
                });
            }

            // 更新持仓
            if (accountInfo.positions) {
                updatePositionsDisplay(exchange, accountInfo.positions);
            }
        }

        // 加载策略参数
        async function loadStrategyParameters() {
            try {
                const response = await fetch('/api/strategy/parameters');
                const data = await response.json();

                if (data.success && data.parameters) {
                    updateStrategyParametersDisplay(data.parameters);
                } else {
                    console.error('加载策略参数失败:', data.error);
                }
            } catch (error) {
                console.error('加载策略参数失败:', error);
            }
        }

        // 更新策略参数显示
        function updateStrategyParametersDisplay(parameters) {
            // 基础参数
            document.getElementById('paramSymbol').textContent = parameters.symbol || '--';
            document.getElementById('paramMaPeriod').textContent = parameters.ma_period || '--';
            document.getElementById('paramMinProfit').textContent = parameters.min_profit_threshold || '--';
            document.getElementById('paramMaxSpread').textContent = parameters.max_spread_threshold || '--';

            // 交易量参数
            document.getElementById('paramMaxAmount').textContent = parameters.max_trade_amount || '--';
            document.getElementById('paramMinAmount').textContent = parameters.min_trade_amount || '--';
            document.getElementById('paramCooldown').textContent = parameters.cooldown_period || '--';
            document.getElementById('paramSignalThreshold').textContent = parameters.signal_threshold || '--';

            // 过滤参数
            document.getElementById('paramMinSpreadFilter').textContent = parameters.min_spread || '--';
            document.getElementById('paramMaxSpreadFilter').textContent = parameters.max_spread || '--';
            document.getElementById('paramMinVolume').textContent = parameters.min_volume || '--';
        }

        // 填充编辑表单
        function fillEditForm(parameters) {
            document.getElementById('editMaPeriod').value = parameters.ma_period || '';
            document.getElementById('editMinProfit').value = parameters.min_profit_threshold || '';
            document.getElementById('editMaxSpread').value = parameters.max_spread_threshold || '';
            document.getElementById('editMaxAmount').value = parameters.max_trade_amount || '';
            document.getElementById('editMinAmount').value = parameters.min_trade_amount || '';
            document.getElementById('editCooldown').value = parameters.cooldown_period || '';
            document.getElementById('editSignalThreshold').value = parameters.signal_threshold || '';
            document.getElementById('editMinSpreadFilter').value = parameters.min_spread || '';
            document.getElementById('editMaxSpreadFilter').value = parameters.max_spread || '';
            document.getElementById('editMinVolume').value = parameters.min_volume || '';
        }

        // 保存策略参数
        async function saveStrategyParameters() {
            const parameters = {
                ma_period: parseInt(document.getElementById('editMaPeriod').value),
                min_profit_threshold: parseFloat(document.getElementById('editMinProfit').value),
                max_spread_threshold: parseFloat(document.getElementById('editMaxSpread').value),
                max_trade_amount: parseFloat(document.getElementById('editMaxAmount').value),
                min_trade_amount: parseFloat(document.getElementById('editMinAmount').value),
                cooldown_period: parseInt(document.getElementById('editCooldown').value),
                signal_threshold: parseFloat(document.getElementById('editSignalThreshold').value),
                min_spread: parseFloat(document.getElementById('editMinSpreadFilter').value),
                max_spread: parseFloat(document.getElementById('editMaxSpreadFilter').value),
                min_volume: parseFloat(document.getElementById('editMinVolume').value)
            };

            // 过滤掉空值
            const filteredParameters = {};
            for (const [key, value] of Object.entries(parameters)) {
                if (!isNaN(value) && value !== '') {
                    filteredParameters[key] = value;
                }
            }

            try {
                const response = await fetch('/api/strategy/parameters', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ parameters: filteredParameters })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('策略参数更新成功', 'success');

                    // 切换回显示模式
                    toggleStrategyParametersEdit(false);

                    // 重新加载参数
                    loadStrategyParameters();
                } else {
                    const errorMsg = result.errors ? result.errors.join(', ') : '更新失败';
                    showNotification(`更新失败: ${errorMsg}`, 'danger');
                }
            } catch (error) {
                console.error('保存策略参数失败:', error);
                showNotification('保存失败: 网络错误', 'danger');
            }
        }

        // 切换策略参数编辑模式
        function toggleStrategyParametersEdit(editMode) {
            const viewDiv = document.getElementById('strategyParametersView');
            const editDiv = document.getElementById('strategyParametersEdit');
            const editBtn = document.getElementById('editStrategyBtn');

            if (editMode) {
                viewDiv.style.display = 'none';
                editDiv.style.display = 'block';
                editBtn.innerHTML = '<i class="fas fa-eye"></i> 查看参数';

                // 加载当前参数到编辑表单
                loadCurrentParametersToForm();
            } else {
                viewDiv.style.display = 'block';
                editDiv.style.display = 'none';
                editBtn.innerHTML = '<i class="fas fa-edit"></i> 编辑参数';
            }
        }

        // 加载当前参数到编辑表单
        async function loadCurrentParametersToForm() {
            try {
                const response = await fetch('/api/strategy/parameters');
                const data = await response.json();

                if (data.success && data.parameters) {
                    fillEditForm(data.parameters);
                }
            } catch (error) {
                console.error('加载参数到表单失败:', error);
            }
        }

        // 格式化时间为本地时间，精确到毫秒
        function formatTimestamp(timestamp) {
            if (!timestamp) return '--';

            let date;

            // 处理不同的时间戳格式
            if (typeof timestamp === 'string') {
                // 如果是ISO格式字符串，直接解析
                date = new Date(timestamp);
            } else if (typeof timestamp === 'number') {
                // 如果是Unix时间戳（秒或毫秒）
                if (timestamp < 10000000000) {
                    // 小于10位数，认为是秒级时间戳
                    date = new Date(timestamp * 1000);
                } else {
                    // 大于等于10位数，认为是毫秒级时间戳
                    date = new Date(timestamp);
                }
            } else {
                // 其他情况，尝试直接转换
                date = new Date(timestamp);
            }

            // 检查日期是否有效
            if (isNaN(date.getTime())) {
                return '--';
            }

            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            const milliseconds = String(date.getMilliseconds()).padStart(3, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
        }

        // 更新交易记录
        function updateTradeRecords(trades) {
            const tradesContainer = document.getElementById('tradesContainer');

            if (!trades || trades.length === 0) {
                tradesContainer.innerHTML = '<div class="text-center text-muted">暂无交易记录</div>';
                return;
            }

            const tradesHtml = trades.map(trade => {
                const timestamp = formatTimestamp(trade.timestamp);
                const profit = parseFloat(trade.actual_profit || 0);
                const profitClass = profit > 0 ? 'trade-profit-positive' :
                                  profit < 0 ? 'trade-profit-negative' : 'trade-profit-neutral';
                const profitColor = profit > 0 ? 'text-success' :
                                   profit < 0 ? 'text-danger' : 'text-muted';

                // 格式化交易类型显示
                const tradeTypeDisplay = trade.trade_type === 'buy_arbitrage' ? '买入套利' :
                                       trade.trade_type === 'sell_arbitrage' ? '卖出套利' :
                                       trade.trade_type || 'Unknown';

                // 计算执行时长
                const executionTime = trade.execution_time_ms ? `${trade.execution_time_ms}ms` : '--';

                // 格式化买入和卖出时间
                const binanceTime = trade.binance_timestamp ? formatTimestamp(trade.binance_timestamp) : '--';
                const lighterTime = trade.lighter_timestamp ? formatTimestamp(trade.lighter_timestamp) : '--';

                // 构建详细的价格和时间信息
                let detailsHtml = '';
                if (trade.binance_price && trade.lighter_price) {
                    detailsHtml = `
                        <div class="mt-2 small">
                            <div class="row mb-2">
                                <div class="col-6">
                                    <span class="text-muted">Binance ${trade.binance_side || ''}:</span>
                                    <div class="fw-bold">${parseFloat(trade.binance_price).toFixed(2)} USDT</div>
                                    <div class="trade-time text-info" style="font-size: 0.8em;">${binanceTime}</div>
                                </div>
                                <div class="col-6">
                                    <span class="text-muted">Lighter ${trade.lighter_side || ''}:</span>
                                    <div class="fw-bold">${parseFloat(trade.lighter_price).toFixed(2)} USDT</div>
                                    <div class="trade-time text-info" style="font-size: 0.8em;">${lighterTime}</div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <span class="text-muted">数量:</span> ${parseFloat(trade.binance_quantity || trade.lighter_quantity || 0).toFixed(4)}
                                </div>
                                <div class="col-6">
                                    <span class="text-muted">执行时长:</span> <span class="text-info">${executionTime}</span>
                                </div>
                            </div>
                        </div>
                    `;
                }

                return `
                    <div class="trade-item ${profitClass} mb-2">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="fw-bold">${tradeTypeDisplay}</div>
                                <div class="trade-time small text-primary">${timestamp}</div>
                                <div class="small text-muted">${trade.symbol || 'BTC/USDT'}</div>
                            </div>
                            <div class="text-end">
                                <div class="trade-amount ${profitColor} fw-bold">
                                    ${profit >= 0 ? '+' : ''}${profit.toFixed(4)} USDT
                                </div>
                                <div class="small text-muted">
                                    ${trade.status === 'executed' ? '已完成' :
                                      trade.status === 'pending' ? '待执行' :
                                      trade.status === 'failed' ? '失败' :
                                      trade.status || 'Unknown'}
                                </div>
                            </div>
                        </div>
                        ${detailsHtml}
                    </div>
                `;
            }).join('');

            tradesContainer.innerHTML = tradesHtml;
        }

        // 加载交易记录
        async function loadTradeRecords() {
            try {
                const response = await fetch('/api/trades?limit=20');
                if (response.ok) {
                    const data = await response.json();
                    updateTradeRecords(data.trades || []);
                } else {
                    console.error('获取交易记录失败:', response.statusText);
                }
            } catch (error) {
                console.error('加载交易记录失败:', error);
                document.getElementById('tradesContainer').innerHTML =
                    '<div class="text-center text-danger">加载失败</div>';
            }
        }

        // 更新挂单显示
        function updateOrders(orders) {
            const ordersContainer = document.getElementById('ordersContainer');

            if (!orders || orders.length === 0) {
                ordersContainer.innerHTML = '<div class="text-center text-muted">暂无挂单</div>';
                return;
            }

            const ordersHtml = orders.map(order => {
                const timestamp = formatTimestamp(order.timestamp);
                const statusClass = order.status === 'open' ? 'text-warning' :
                                  order.status === 'filled' ? 'text-success' : 'text-danger';
                const statusText = order.status === 'open' ? '挂单中' :
                                 order.status === 'filled' ? '已成交' : '已取消';

                return `
                    <div class="border-bottom pb-2 mb-2">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong class="${order.side === 'buy' ? 'text-success' : 'text-danger'}">
                                    ${order.side === 'buy' ? '买入' : '卖出'}
                                </strong>
                                <span class="text-muted">${order.exchange}</span>
                            </div>
                            <div class="text-end">
                                <div class="small text-muted">${timestamp}</div>
                                <span class="badge ${statusClass === 'text-warning' ? 'bg-warning' :
                                                   statusClass === 'text-success' ? 'bg-success' : 'bg-danger'}">
                                    ${statusText}
                                </span>
                            </div>
                        </div>
                        <div class="mt-1">
                            <div>价格: <span class="fw-bold">${order.price}</span> USDT</div>
                            <div>数量: <span class="fw-bold">${order.amount}</span> BTC</div>
                            ${order.filled_amount ? `<div>已成交: <span class="text-success">${order.filled_amount}</span> BTC</div>` : ''}
                        </div>
                    </div>
                `;
            }).join('');

            ordersContainer.innerHTML = ordersHtml;
        }

        // 加载当前挂单
        async function loadOrders() {
            try {
                const response = await fetch('/api/orders');
                if (response.ok) {
                    const data = await response.json();
                    updateOrders(data.orders || []);
                } else {
                    console.error('获取挂单失败:', response.statusText);
                }
            } catch (error) {
                console.error('加载挂单失败:', error);
                document.getElementById('ordersContainer').innerHTML =
                    '<div class="text-center text-danger">加载失败</div>';
            }
        }

        // 获取日志级别对应的Bootstrap样式类
        function getLevelClass(level) {
            const levelMap = {
                'debug': 'secondary',
                'info': 'primary',
                'warning': 'warning',
                'error': 'danger',
                'critical': 'danger'
            };
            return levelMap[level.toLowerCase()] || 'secondary';
        }

        // 更新系统日志
        function updateSystemLogs(logEntry) {
            const logsContainer = document.getElementById('systemLogs');

            // 如果是第一条日志，清除"等待日志数据"提示
            const waitingMessage = logsContainer.querySelector('.text-muted');
            if (waitingMessage && waitingMessage.textContent === '等待日志数据...') {
                waitingMessage.remove();
            }

            // 创建日志条目
            const logElement = document.createElement('div');
            // 使用精确到毫秒的时间格式
            const timestamp = formatTimestamp(new Date(logEntry.timestamp * 1000));
            const levelClass = getLevelClass(logEntry.level.toLowerCase());

            logElement.innerHTML = `
                <div class="log-entry mb-1">
                    <span class="text-muted small">[${timestamp}]</span>
                    <span class="badge bg-${levelClass} me-1">${logEntry.level}</span>
                    <span>${logEntry.message}</span>
                    ${logEntry.data && Object.keys(logEntry.data).length > 0 ?
                        `<small class="text-muted d-block ms-3">${JSON.stringify(logEntry.data)}</small>` : ''}
                </div>
            `;

            // 添加到日志容器
            logsContainer.appendChild(logElement);

            // 限制日志条目数量
            const logEntries = logsContainer.querySelectorAll('.log-entry');
            if (logEntries.length > 100) {
                logEntries[0].remove();
            }

            // 自动滚动到底部
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        // 清空日志
        function clearLogs() {
            const logsContainer = document.getElementById('systemLogs');
            logsContainer.innerHTML = '<div class="text-muted">日志已清空</div>';
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 60px; right: 20px; z-index: 1050; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // 自动移除通知
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        // 紧急停止
        async function emergencyStop() {
            if (!confirm('确定要触发紧急停止吗？这将立即停止所有交易活动。')) {
                return;
            }

            try {
                const response = await fetch('/api/emergency_stop', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('紧急停止已触发', 'warning');
                } else {
                    showNotification(`紧急停止失败: ${result.error}`, 'danger');
                }
            } catch (error) {
                showNotification(`紧急停止失败: ${error.message}`, 'danger');
            }
        }

        // 重置紧急停止
        async function resetEmergency() {
            if (!confirm('确定要重置紧急停止状态吗？')) {
                return;
            }

            try {
                const response = await fetch('/api/reset_emergency', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('紧急停止已重置', 'success');
                } else {
                    showNotification(`重置失败: ${result.error}`, 'danger');
                }
            } catch (error) {
                showNotification(`重置失败: ${error.message}`, 'danger');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initChart();

            // 连接WebSocket
            connectWebSocket();

            // 主动获取初始状态数据
            loadInitialData();

            // 加载交易记录
            loadTradeRecords();

            // 加载当前挂单
            loadOrders();

            // 加载策略参数
            loadStrategyParameters();

            // 加载总资产信息
            loadTotalAssets();

            // 绑定按钮事件
            document.getElementById('emergencyStopBtn').addEventListener('click', emergencyStop);
            document.getElementById('resetEmergencyBtn').addEventListener('click', resetEmergency);
            document.getElementById('refreshTradesBtn').addEventListener('click', loadTradeRecords);
            document.getElementById('refreshOrdersBtn').addEventListener('click', loadOrders);
            document.getElementById('clearLogsBtn').addEventListener('click', clearLogs);

            // 绑定余额刷新按钮事件
            document.getElementById('refreshBinanceBtn').addEventListener('click', function() {
                loadExchangeAccountInfo('binance');
                loadTotalAssets(); // 同时刷新总资产
            });
            document.getElementById('refreshLighterBtn').addEventListener('click', function() {
                loadExchangeAccountInfo('lighter');
                loadTotalAssets(); // 同时刷新总资产
            });

            // 绑定策略参数编辑事件
            document.getElementById('editStrategyBtn').addEventListener('click', function() {
                const isEditing = document.getElementById('strategyParametersEdit').style.display !== 'none';
                toggleStrategyParametersEdit(!isEditing);
            });

            document.getElementById('cancelEditBtn').addEventListener('click', function() {
                toggleStrategyParametersEdit(false);
            });

            document.getElementById('strategyParametersForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveStrategyParameters();
            });

            // 定期刷新交易记录（降低频率，因为现在有实时推送）
            setInterval(() => {
                loadTradeRecords();
            }, 60000); // 每60秒刷新一次（作为备份机制）

            // 定期刷新挂单信息
            setInterval(() => {
                loadOrders();
            }, 30000); // 每30秒刷新一次挂单

            // 定期刷新总资产信息
            setInterval(() => {
                loadTotalAssets();
            }, 30000); // 每30秒刷新一次总资产

            // 定期发送心跳
            setInterval(() => {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({type: 'ping'}));
                }
            }, 30000);

            // 初始化数据重置功能
            initResetDataFeature();
        });

        // 加载初始数据
        async function loadInitialData() {
            try {
                // 获取系统状态
                const statusResponse = await fetch('/api/status');
                if (statusResponse.ok) {
                    const statusData = await statusResponse.json();
                    console.log('获取到初始状态数据:', statusData);
                    updateSystemStatus(statusData);
                }

                // 获取价格数据
                const pricesResponse = await fetch('/api/prices');
                if (pricesResponse.ok) {
                    const pricesData = await pricesResponse.json();
                    console.log('获取到初始价格数据:', pricesData);
                    updatePricesOnly(pricesData);
                }

                // 获取初始日志
                const logsResponse = await fetch('/api/logs?limit=50');
                if (logsResponse.ok) {
                    const logsData = await logsResponse.json();
                    if (logsData.logs && logsData.logs.length > 0) {
                        const logsContainer = document.getElementById('systemLogs');
                        logsContainer.innerHTML = ''; // 清空等待消息

                        logsData.logs.forEach(logEntry => {
                            updateSystemLogs(logEntry);
                        });
                    }
                }

            } catch (error) {
                console.error('加载初始数据失败:', error);
                showNotification('加载初始数据失败: ' + error.message, 'warning');
            }
        }

        // 数据重置相关变量
        let currentResetType = 'all';
        let resetDataModal = null;

        // 数据重置功能
        function initResetDataFeature() {
            resetDataModal = new bootstrap.Modal(document.getElementById('resetDataModal'));

            // 重置按钮点击事件
            document.getElementById('resetTradingDataBtn').addEventListener('click', function() {
                currentResetType = 'trading';
                showResetDataModal();
            });

            document.getElementById('resetAllDataBtn').addEventListener('click', function() {
                currentResetType = 'all';
                showResetDataModal();
            });

            // 确认重置按钮
            document.getElementById('confirmResetBtn').addEventListener('click', function() {
                performDataReset();
            });
        }

        // 显示重置数据模态框
        async function showResetDataModal() {
            try {
                // 重置模态框状态
                document.getElementById('resetDataContent').style.display = 'block';
                document.getElementById('resetProgress').style.display = 'none';
                document.getElementById('resetResult').style.display = 'none';
                document.getElementById('confirmResetBtn').style.display = 'inline-block';
                document.getElementById('resetCancelBtn').textContent = '取消';

                // 设置重置类型说明
                const resetTypeDescription = document.getElementById('resetTypeDescription');
                if (currentResetType === 'all') {
                    resetTypeDescription.innerHTML = `
                        <strong>重置全部数据</strong><br>
                        将删除所有数据表中的记录，包括：<br>
                        • 交易记录、价格历史、价差记录<br>
                        • 系统状态、性能指标<br>
                        • 系统将完全重新开始记录数据
                    `;
                    document.getElementById('resetDataModalLabel').innerHTML =
                        '<i class="fas fa-database"></i> 重置全部数据';
                } else {
                    resetTypeDescription.innerHTML = `
                        <strong>重置交易数据</strong><br>
                        仅删除交易相关数据，保留价格历史：<br>
                        • 删除：交易记录、性能指标<br>
                        • 保留：价格历史、价差记录、系统状态<br>
                        • 适合重新开始交易测试
                    `;
                    document.getElementById('resetDataModalLabel').innerHTML =
                        '<i class="fas fa-refresh"></i> 重置交易数据';
                }

                // 加载当前数据统计
                await loadResetStatus();

                // 显示模态框
                resetDataModal.show();

            } catch (error) {
                console.error('显示重置模态框失败:', error);
                showNotification('加载重置信息失败: ' + error.message, 'danger');
            }
        }

        // 加载重置状态信息
        async function loadResetStatus() {
            try {
                const response = await fetch('/api/reset_status');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                // 更新数据统计
                const stats = data.current_stats || {};
                document.getElementById('statsTradesCount').textContent = stats.trades || 0;
                document.getElementById('statsPricesCount').textContent = stats.prices || 0;
                document.getElementById('statsSpreadsCount').textContent = stats.spreads || 0;
                // document.getElementById('statsRiskCount').textContent = stats.risk_metrics || 0; // 风险管理模块已移除
                document.getElementById('statsSystemCount').textContent = stats.system_status || 0;
                document.getElementById('statsPerformanceCount').textContent = stats.performance_metrics || 0;
                document.getElementById('statsTotalCount').textContent = data.total_records || 0;

                // 更新最近备份
                const recentBackups = data.recent_backups || [];
                const backupsContainer = document.getElementById('recentBackups');

                if (recentBackups.length === 0) {
                    backupsContainer.innerHTML = '<div class="text-muted">暂无备份文件</div>';
                } else {
                    const backupsHtml = recentBackups.map(backup => `
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span><i class="fas fa-file-archive"></i> ${backup}</span>
                        </div>
                    `).join('');
                    backupsContainer.innerHTML = backupsHtml;
                }

                // 检查是否可以重置（仅模拟交易模式）
                const confirmBtn = document.getElementById('confirmResetBtn');
                if (!data.reset_available) {
                    confirmBtn.disabled = true;
                    confirmBtn.innerHTML = '<i class="fas fa-lock"></i> 仅限模拟交易模式';

                    // 添加警告
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-danger';
                    alertDiv.innerHTML = `
                        <h6><i class="fas fa-exclamation-triangle"></i> 无法重置</h6>
                        <p class="mb-0">数据重置功能仅在模拟交易模式下可用。<br>
                        当前模式: <strong>${data.is_paper_trading ? '模拟交易' : '实盘交易'}</strong></p>
                    `;
                    document.getElementById('resetDataContent').insertBefore(
                        alertDiv,
                        document.getElementById('resetDataContent').firstChild
                    );
                } else {
                    confirmBtn.disabled = false;
                    confirmBtn.innerHTML = '<i class="fas fa-trash-restore"></i> 确认重置';
                }

            } catch (error) {
                console.error('加载重置状态失败:', error);
                showNotification('加载重置状态失败: ' + error.message, 'danger');
            }
        }

        // 执行数据重置
        async function performDataReset() {
            try {
                // 显示进度
                document.getElementById('resetDataContent').style.display = 'none';
                document.getElementById('resetProgress').style.display = 'block';
                document.getElementById('confirmResetBtn').style.display = 'none';

                const createBackup = document.getElementById('createBackupCheck').checked;

                const response = await fetch(`/api/reset_data?reset_type=${currentResetType}&create_backup=${createBackup}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (result.error) {
                    throw new Error(result.error);
                }

                // 显示成功结果
                document.getElementById('resetProgress').style.display = 'none';
                document.getElementById('resetResult').style.display = 'block';
                document.getElementById('resetCancelBtn').textContent = '关闭';

                const resetInfo = result.reset_result || {};
                const resultMessage = document.getElementById('resetResultMessage');
                resultMessage.innerHTML = `
                    <p><strong>重置完成！</strong></p>
                    <ul>
                        <li>重置类型: ${currentResetType === 'all' ? '全部数据' : '交易数据'}</li>
                        <li>删除记录数: ${resetInfo.total_records_deleted || 0}</li>
                        <li>备份创建: ${resetInfo.backup_created ? '是' : '否'}</li>
                        ${resetInfo.backup_path ? `<li>备份路径: ${resetInfo.backup_path}</li>` : ''}
                        <li>完成时间: ${new Date().toLocaleString()}</li>
                    </ul>
                    <p class="text-success">系统统计数据已重置，可以重新开始测试。</p>
                `;

                // 显示成功通知
                showNotification('数据重置成功完成', 'success');

                // 3秒后自动刷新页面
                setTimeout(() => {
                    window.location.reload();
                }, 3000);

            } catch (error) {
                console.error('数据重置失败:', error);

                // 显示错误
                document.getElementById('resetProgress').style.display = 'none';
                document.getElementById('resetResult').style.display = 'block';
                document.getElementById('resetResult').innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> 重置失败</h6>
                        <p class="mb-0">错误信息: ${error.message}</p>
                    </div>
                `;
                document.getElementById('resetCancelBtn').textContent = '关闭';

                showNotification('数据重置失败: ' + error.message, 'danger');
            }
        }

        // 页面卸载时关闭WebSocket
        window.addEventListener('beforeunload', function() {
            if (ws) {
                ws.close();
            }
        });
    </script>
</body>
</html>