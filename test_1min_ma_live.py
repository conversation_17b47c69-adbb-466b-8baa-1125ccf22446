#!/usr/bin/env python3
"""
1分钟MA实时测试脚本

测试实际运行中1分钟MA的效果
"""

import asyncio
import time
import logging
from datetime import datetime
from src.arbitrage.engine import ArbitrageEngine
from src.utils.config_loader import ConfigLoader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LiveMATest:
    """实时MA测试"""
    
    def __init__(self):
        self.engine = None
        self.ma_updates = []
        self.start_time = None
        
    async def initialize(self):
        """初始化引擎"""
        logger.info("🚀 初始化套利引擎进行1分钟MA测试")
        
        # 加载配置
        config = ConfigLoader.load_config()
        
        # 确保使用1分钟窗口
        if 'strategy' not in config:
            config['strategy'] = {}
        if 'parameters' not in config['strategy']:
            config['strategy']['parameters'] = {}
        
        config['strategy']['parameters']['use_time_window'] = True
        config['strategy']['parameters']['window_minutes'] = 1
        
        logger.info("✅ 配置已设置为1分钟时间窗口MA")
        
        # 初始化引擎
        self.engine = ArbitrageEngine(config)
        await self.engine.initialize()
        
        logger.info("✅ 引擎初始化完成")
        
    async def monitor_ma_updates(self, duration_minutes=5):
        """监控MA更新"""
        logger.info(f"📊 开始监控{duration_minutes}分钟的MA更新")
        
        self.start_time = time.time()
        end_time = self.start_time + (duration_minutes * 60)
        
        last_ma_value = None
        ma_change_count = 0
        window_completion_count = 0
        
        while time.time() < end_time:
            try:
                # 获取策略状态
                if self.engine.strategy:
                    status = self.engine.strategy.get_strategy_status()
                    
                    # 获取当前MA值
                    current_ma = status.get('spread_ma_value')
                    
                    # 获取时间窗口信息
                    if hasattr(self.engine.strategy.spread_analyzer, 'time_analyzer'):
                        time_analyzer = self.engine.strategy.spread_analyzer.time_analyzer
                        
                        # 检查窗口进度
                        if time_analyzer.current_window_start is not None:
                            current_time = time.time()
                            window_progress = (current_time - time_analyzer.current_window_start) / time_analyzer.window_seconds * 100
                            window_size = len(time_analyzer.current_window_spreads)
                            
                            # 记录MA更新
                            elapsed = time.time() - self.start_time
                            self.ma_updates.append({
                                'timestamp': current_time,
                                'elapsed_seconds': elapsed,
                                'ma_value': current_ma,
                                'window_progress': window_progress,
                                'window_size': window_size,
                                'ma_ready': status.get('spread_ma_ready', False)
                            })
                            
                            # 检查MA变化
                            if current_ma is not None and last_ma_value is not None:
                                if abs(current_ma - last_ma_value) > 1e-8:
                                    ma_change_count += 1
                                    logger.info(f"📈 MA更新: {current_ma:.6f} (第{ma_change_count}次变化)")
                            
                            # 检查窗口完成
                            if window_progress < 10 and window_size > 0:  # 新窗口开始
                                window_completion_count += 1
                                logger.info(f"🔄 窗口完成: 第{window_completion_count}个窗口")
                            
                            # 每30秒报告一次状态
                            if int(elapsed) % 30 == 0 and int(elapsed) > 0:
                                logger.info(f"⏱️ 运行{elapsed/60:.1f}分钟:")
                                logger.info(f"   MA值: {current_ma:.6f if current_ma else 'None'}")
                                logger.info(f"   MA就绪: {status.get('spread_ma_ready', False)}")
                                logger.info(f"   窗口进度: {window_progress:.1f}%")
                                logger.info(f"   窗口大小: {window_size}")
                                logger.info(f"   MA变化次数: {ma_change_count}")
                                logger.info(f"   窗口完成次数: {window_completion_count}")
                            
                            last_ma_value = current_ma
                
                await asyncio.sleep(1)  # 每秒检查一次
                
            except Exception as e:
                logger.error(f"监控过程中发生错误: {e}")
                await asyncio.sleep(5)
        
        # 生成最终报告
        await self.generate_report(ma_change_count, window_completion_count)
    
    async def generate_report(self, ma_change_count, window_completion_count):
        """生成测试报告"""
        logger.info("=" * 60)
        logger.info("📊 1分钟MA实时测试报告")
        logger.info("=" * 60)
        
        total_duration = time.time() - self.start_time
        total_updates = len(self.ma_updates)
        
        logger.info(f"测试时长: {total_duration/60:.1f} 分钟")
        logger.info(f"总记录数: {total_updates}")
        logger.info(f"MA变化次数: {ma_change_count}")
        logger.info(f"窗口完成次数: {window_completion_count}")
        
        # 分析MA可用性
        ma_available_updates = [u for u in self.ma_updates if u['ma_value'] is not None]
        ma_ready_updates = [u for u in self.ma_updates if u['ma_ready']]
        
        logger.info(f"MA可用记录数: {len(ma_available_updates)} ({len(ma_available_updates)/total_updates*100:.1f}%)")
        logger.info(f"MA就绪记录数: {len(ma_ready_updates)} ({len(ma_ready_updates)/total_updates*100:.1f}%)")
        
        # 分析首次MA可用时间
        first_ma_available = next((u for u in self.ma_updates if u['ma_value'] is not None), None)
        first_ma_ready = next((u for u in self.ma_updates if u['ma_ready']), None)
        
        if first_ma_available:
            logger.info(f"首次MA可用时间: {first_ma_available['elapsed_seconds']:.1f}秒")
        
        if first_ma_ready:
            logger.info(f"首次MA就绪时间: {first_ma_ready['elapsed_seconds']:.1f}秒")
        
        # 分析窗口更新频率
        if window_completion_count > 0:
            window_frequency = total_duration / window_completion_count / 60
            logger.info(f"平均窗口完成间隔: {window_frequency:.1f}分钟")
        
        # 分析MA更新频率
        if ma_change_count > 0:
            ma_frequency = total_duration / ma_change_count
            logger.info(f"平均MA更新间隔: {ma_frequency:.1f}秒")
        
        # 评估1分钟MA效果
        logger.info("\n🔍 1分钟MA效果评估:")
        
        if first_ma_ready and first_ma_ready['elapsed_seconds'] <= 80:  # 约1分20秒内就绪
            logger.info("✅ MA响应速度快 - 在预期时间内就绪")
        elif first_ma_ready:
            logger.info(f"⚠️ MA响应较慢 - {first_ma_ready['elapsed_seconds']:.1f}秒后才就绪")
        else:
            logger.info("❌ MA在测试期间未就绪")
        
        if window_completion_count >= total_duration / 60 * 0.8:  # 至少80%的预期窗口数
            logger.info("✅ 窗口更新频率正常")
        else:
            logger.info("⚠️ 窗口更新频率较低")
        
        if ma_change_count > 0:
            logger.info(f"✅ MA有效更新 - 共{ma_change_count}次变化")
        else:
            logger.info("⚠️ MA未发生变化")
        
        # 显示最终状态
        if self.ma_updates:
            final_update = self.ma_updates[-1]
            logger.info(f"\n最终状态:")
            logger.info(f"  MA值: {final_update['ma_value']:.6f if final_update['ma_value'] else 'None'}")
            logger.info(f"  MA就绪: {final_update['ma_ready']}")
            logger.info(f"  窗口进度: {final_update['window_progress']:.1f}%")
        
        logger.info("=" * 60)
    
    async def cleanup(self):
        """清理资源"""
        if self.engine:
            await self.engine.close()
            logger.info("✅ 引擎已关闭")

async def main():
    """主函数"""
    tester = LiveMATest()
    
    try:
        # 初始化
        await tester.initialize()
        
        # 监控5分钟
        await tester.monitor_ma_updates(duration_minutes=5)
        
        logger.info("🎉 1分钟MA实时测试完成")
        
    except KeyboardInterrupt:
        logger.info("🛑 测试被用户中断")
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
    finally:
        # 清理
        await tester.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
