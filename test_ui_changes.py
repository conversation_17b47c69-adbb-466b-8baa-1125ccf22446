#!/usr/bin/env python3
"""
UI修改验证脚本

验证价差率正负号显示和时间窗口/MA周期同行显示的修改效果
"""

import requests
import time
import logging
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UIChangeValidator:
    """UI修改验证器"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        
    def check_server_status(self):
        """检查服务器状态"""
        try:
            response = requests.get(self.base_url, timeout=5)
            if response.status_code == 200:
                logger.info("✅ Web服务器运行正常")
                return True
            else:
                logger.error(f"❌ Web服务器响应异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 无法连接到Web服务器: {e}")
            return False
    
    def get_dashboard_html(self):
        """获取仪表板HTML内容"""
        try:
            response = requests.get(self.base_url, timeout=10)
            if response.status_code == 200:
                return response.text
            else:
                logger.error(f"❌ 获取仪表板失败: {response.status_code}")
                return None
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 获取仪表板异常: {e}")
            return None
    
    def validate_html_structure(self, html_content):
        """验证HTML结构修改"""
        logger.info("🔍 验证HTML结构修改...")
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 检查价差率显示元素
        spread_rate_element = soup.find('span', {'id': 'currentSpreadRate'})
        if spread_rate_element:
            logger.info("✅ 找到价差率显示元素")
        else:
            logger.error("❌ 未找到价差率显示元素")
            return False
        
        # 检查时间窗口和MA周期是否在同一行
        time_window_element = soup.find('span', {'id': 'timeWindowDisplay'})
        ma_period_element = soup.find('span', {'id': 'maPeriodDisplay'})
        
        if time_window_element and ma_period_element:
            # 检查它们是否在同一个父元素中
            time_window_parent = time_window_element.parent
            ma_period_parent = ma_period_element.parent
            
            if time_window_parent == ma_period_parent:
                logger.info("✅ 时间窗口和MA周期在同一行显示")
                
                # 检查是否有分隔符
                parent_text = time_window_parent.get_text()
                if '|' in parent_text:
                    logger.info("✅ 找到分隔符 '|'")
                else:
                    logger.warning("⚠️  未找到分隔符 '|'")
                
                return True
            else:
                logger.error("❌ 时间窗口和MA周期不在同一行")
                return False
        else:
            logger.error("❌ 未找到时间窗口或MA周期元素")
            return False
    
    def validate_javascript_logic(self, html_content):
        """验证JavaScript逻辑修改"""
        logger.info("🔍 验证JavaScript逻辑修改...")
        
        # 检查是否包含正负号逻辑
        if 'currentSpreadRate >= 0' in html_content:
            logger.info("✅ 找到正负号判断逻辑")
        else:
            logger.error("❌ 未找到正负号判断逻辑")
            return False
        
        # 检查是否包含颜色设置逻辑
        if 'text-success' in html_content and 'text-danger' in html_content:
            logger.info("✅ 找到颜色设置逻辑")
        else:
            logger.error("❌ 未找到颜色设置逻辑")
            return False
        
        # 检查正负号格式化逻辑
        if '+${spreadRatePercent}%' in html_content:
            logger.info("✅ 找到正号格式化逻辑")
        else:
            logger.error("❌ 未找到正号格式化逻辑")
            return False
        
        return True
    
    def get_api_data(self):
        """获取API数据来验证实际显示效果"""
        try:
            response = requests.get(f"{self.base_url}/api/data", timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"❌ 获取API数据失败: {response.status_code}")
                return None
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 获取API数据异常: {e}")
            return None
    
    def simulate_spread_rate_display(self, api_data):
        """模拟价差率显示效果"""
        logger.info("🔍 模拟价差率显示效果...")
        
        if not api_data:
            logger.warning("⚠️  无API数据，无法模拟显示效果")
            return
        
        # 获取价格数据
        binance_price = api_data.get('binance', {}).get('last')
        lighter_price = api_data.get('lighter', {}).get('last')
        
        if binance_price and lighter_price:
            try:
                binance_price = float(binance_price)
                lighter_price = float(lighter_price)
                
                # 计算价差率
                spread_rate = (lighter_price - binance_price) / binance_price
                spread_rate_percent = spread_rate * 100
                
                # 模拟显示格式
                if spread_rate >= 0:
                    display_text = f"+{spread_rate_percent:.4f}%"
                    color_class = "text-success"
                else:
                    display_text = f"{spread_rate_percent:.4f}%"
                    color_class = "text-danger"
                
                logger.info(f"📊 模拟显示效果:")
                logger.info(f"   Binance价格: {binance_price}")
                logger.info(f"   Lighter价格: {lighter_price}")
                logger.info(f"   价差率: {spread_rate:.8f}")
                logger.info(f"   显示文本: {display_text}")
                logger.info(f"   颜色类: {color_class}")
                
                return True
                
            except (ValueError, TypeError) as e:
                logger.error(f"❌ 价格数据格式错误: {e}")
                return False
        else:
            logger.warning("⚠️  价格数据不完整")
            return False
    
    def run_validation(self):
        """运行完整验证"""
        logger.info("=" * 60)
        logger.info("🔍 开始UI修改验证")
        logger.info("=" * 60)
        
        # 1. 检查服务器状态
        if not self.check_server_status():
            logger.error("❌ 服务器不可用，无法进行验证")
            return False
        
        # 2. 获取HTML内容
        html_content = self.get_dashboard_html()
        if not html_content:
            logger.error("❌ 无法获取HTML内容")
            return False
        
        # 3. 验证HTML结构
        if not self.validate_html_structure(html_content):
            logger.error("❌ HTML结构验证失败")
            return False
        
        # 4. 验证JavaScript逻辑
        if not self.validate_javascript_logic(html_content):
            logger.error("❌ JavaScript逻辑验证失败")
            return False
        
        # 5. 获取API数据并模拟显示
        api_data = self.get_api_data()
        self.simulate_spread_rate_display(api_data)
        
        logger.info("=" * 60)
        logger.info("✅ UI修改验证完成")
        logger.info("=" * 60)
        
        logger.info("📋 修改总结:")
        logger.info("  1. ✅ 价差率显示正负号")
        logger.info("  2. ✅ 时间窗口和MA周期同行显示")
        logger.info("  3. ✅ 根据正负值设置颜色")
        logger.info("  4. ✅ 添加分隔符美化显示")
        
        logger.info("🎯 显示效果:")
        logger.info("  - 正值: +0.0242% (绿色)")
        logger.info("  - 负值: -0.0242% (红色)")
        logger.info("  - 零值: 0.0000% (蓝色)")
        logger.info("  - 同行: 时间窗口: 1分钟 | MA周期: 5")
        
        return True

def main():
    """主函数"""
    validator = UIChangeValidator()
    
    try:
        success = validator.run_validation()
        
        if success:
            logger.info("🎉 UI修改验证成功!")
        else:
            logger.error("❌ UI修改验证失败!")
            
    except Exception as e:
        logger.error(f"❌ 验证过程中发生错误: {e}")

if __name__ == "__main__":
    main()
