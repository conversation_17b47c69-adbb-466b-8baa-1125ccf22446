#!/usr/bin/env python3
"""
1分钟MA测试脚本

测试价差MA从5分钟改为1分钟后的效果
"""

import asyncio
import time
import logging
from datetime import datetime
from src.utils.indicators import TimeWindowSpreadAnalyzer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MATestRunner:
    """MA测试运行器"""
    
    def __init__(self):
        # 创建1分钟和5分钟的分析器进行对比
        self.analyzer_1min = TimeWindowSpreadAnalyzer(window_minutes=1, ma_period=20)
        self.analyzer_5min = TimeWindowSpreadAnalyzer(window_minutes=5, ma_period=20)
        
        self.test_data = []
        self.results_1min = []
        self.results_5min = []
        
    def generate_test_prices(self, duration_minutes=10):
        """生成测试价格数据"""
        logger.info(f"生成{duration_minutes}分钟的测试价格数据")
        
        import random
        import math
        
        # 基础价格
        base_binance = 104000.0
        base_lighter = 103950.0
        
        # 生成每秒的价格数据
        for i in range(duration_minutes * 60):
            # 模拟价格波动
            time_factor = i / 60.0  # 时间因子（分钟）
            
            # 添加趋势和随机波动
            trend = math.sin(time_factor * 0.1) * 50  # 慢趋势
            noise_binance = random.gauss(0, 10)  # 随机噪声
            noise_lighter = random.gauss(0, 8)
            
            binance_price = base_binance + trend + noise_binance
            lighter_price = base_lighter + trend * 0.8 + noise_lighter
            
            self.test_data.append({
                'timestamp': time.time() + i,
                'binance_price': binance_price,
                'lighter_price': lighter_price,
                'time_offset': i
            })
    
    def run_test(self):
        """运行测试"""
        logger.info("开始MA对比测试")
        
        # 生成测试数据
        self.generate_test_prices(duration_minutes=10)
        
        logger.info(f"处理{len(self.test_data)}个价格数据点")
        
        # 处理每个数据点
        for i, data in enumerate(self.test_data):
            binance_price = data['binance_price']
            lighter_price = data['lighter_price']
            
            # 更新两个分析器
            result_1min = self.analyzer_1min.update_spread(binance_price, lighter_price)
            result_5min = self.analyzer_5min.update_spread(binance_price, lighter_price)
            
            # 记录结果
            self.results_1min.append({
                'time_offset': data['time_offset'],
                'diff_rate': result_1min.get('diff_rate'),
                'ma': result_1min.get('ma'),
                'window_progress': result_1min.get('window_progress', 0),
                'window_size': result_1min.get('window_size', 0)
            })
            
            self.results_5min.append({
                'time_offset': data['time_offset'],
                'diff_rate': result_5min.get('diff_rate'),
                'ma': result_5min.get('ma'),
                'window_progress': result_5min.get('window_progress', 0),
                'window_size': result_5min.get('window_size', 0)
            })
            
            # 每分钟报告一次进度
            if i % 60 == 0:
                minutes = i // 60
                logger.info(f"处理进度: {minutes}分钟")
                
                # 显示当前状态
                if result_1min.get('ma') is not None:
                    logger.info(f"  1分钟MA: {result_1min['ma']:.6f}, 窗口进度: {result_1min.get('window_progress', 0):.1f}%")
                
                if result_5min.get('ma') is not None:
                    logger.info(f"  5分钟MA: {result_5min['ma']:.6f}, 窗口进度: {result_5min.get('window_progress', 0):.1f}%")
    
    def analyze_results(self):
        """分析测试结果"""
        logger.info("=" * 60)
        logger.info("📊 MA对比测试结果分析")
        logger.info("=" * 60)
        
        # 统计MA可用性
        ma_1min_available = sum(1 for r in self.results_1min if r['ma'] is not None)
        ma_5min_available = sum(1 for r in self.results_5min if r['ma'] is not None)
        
        total_points = len(self.results_1min)
        
        logger.info(f"总数据点: {total_points}")
        logger.info(f"1分钟MA可用点数: {ma_1min_available} ({ma_1min_available/total_points*100:.1f}%)")
        logger.info(f"5分钟MA可用点数: {ma_5min_available} ({ma_5min_available/total_points*100:.1f}%)")
        
        # 分析响应速度
        first_ma_1min = next((i for i, r in enumerate(self.results_1min) if r['ma'] is not None), None)
        first_ma_5min = next((i for i, r in enumerate(self.results_5min) if r['ma'] is not None), None)
        
        if first_ma_1min is not None:
            logger.info(f"1分钟MA首次可用时间: {first_ma_1min}秒")
        
        if first_ma_5min is not None:
            logger.info(f"5分钟MA首次可用时间: {first_ma_5min}秒")
        
        # 分析窗口完成频率
        windows_1min = sum(1 for r in self.results_1min if r['window_progress'] == 0 and r['window_size'] > 0)
        windows_5min = sum(1 for r in self.results_5min if r['window_progress'] == 0 and r['window_size'] > 0)
        
        logger.info(f"1分钟窗口完成次数: {windows_1min}")
        logger.info(f"5分钟窗口完成次数: {windows_5min}")
        
        # 分析MA变化频率
        ma_changes_1min = 0
        ma_changes_5min = 0
        
        last_ma_1min = None
        last_ma_5min = None
        
        for r in self.results_1min:
            if r['ma'] is not None:
                if last_ma_1min is not None and abs(r['ma'] - last_ma_1min) > 1e-8:
                    ma_changes_1min += 1
                last_ma_1min = r['ma']
        
        for r in self.results_5min:
            if r['ma'] is not None:
                if last_ma_5min is not None and abs(r['ma'] - last_ma_5min) > 1e-8:
                    ma_changes_5min += 1
                last_ma_5min = r['ma']
        
        logger.info(f"1分钟MA变化次数: {ma_changes_1min}")
        logger.info(f"5分钟MA变化次数: {ma_changes_5min}")
        
        # 分析优势
        logger.info("\n🔍 分析结果:")
        
        if first_ma_1min and first_ma_5min:
            speed_advantage = first_ma_5min - first_ma_1min
            logger.info(f"✅ 1分钟MA响应速度优势: {speed_advantage}秒")
        
        if ma_changes_1min > ma_changes_5min:
            logger.info(f"✅ 1分钟MA更新更频繁: {ma_changes_1min} vs {ma_changes_5min}")
        
        if windows_1min > windows_5min:
            logger.info(f"✅ 1分钟MA窗口更新更频繁: {windows_1min} vs {windows_5min}")
        
        # 显示最后的MA值
        final_ma_1min = next((r['ma'] for r in reversed(self.results_1min) if r['ma'] is not None), None)
        final_ma_5min = next((r['ma'] for r in reversed(self.results_5min) if r['ma'] is not None), None)
        
        if final_ma_1min is not None:
            logger.info(f"最终1分钟MA值: {final_ma_1min:.6f}")
        
        if final_ma_5min is not None:
            logger.info(f"最终5分钟MA值: {final_ma_5min:.6f}")
        
        logger.info("=" * 60)
        
        # 评估改进效果
        if first_ma_1min and first_ma_5min and first_ma_1min < first_ma_5min:
            logger.info("🎉 1分钟MA改进效果显著:")
            logger.info("  - 更快的信号响应")
            logger.info("  - 更频繁的策略更新")
            logger.info("  - 更好的市场适应性")
        else:
            logger.info("⚠️ 需要进一步观察实际效果")

def main():
    """主函数"""
    tester = MATestRunner()
    
    try:
        # 运行测试
        tester.run_test()
        
        # 分析结果
        tester.analyze_results()
        
        logger.info("✅ 1分钟MA测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
