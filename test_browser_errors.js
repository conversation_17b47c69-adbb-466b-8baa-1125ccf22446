// 简单的浏览器错误检测脚本
const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch();
  const page = await browser.newPage();
  
  // 收集控制台消息
  const consoleMessages = [];
  page.on('console', msg => {
    consoleMessages.push(`${msg.type()}: ${msg.text()}`);
  });
  
  // 收集页面错误
  const pageErrors = [];
  page.on('pageerror', error => {
    pageErrors.push(error.toString());
  });
  
  try {
    // 导航到页面
    await page.goto('http://localhost:8000', { waitUntil: 'networkidle2' });
    
    // 等待页面加载
    await page.waitForTimeout(5000);
    
    console.log('=== 页面错误 ===');
    if (pageErrors.length === 0) {
      console.log('✅ 没有页面错误');
    } else {
      pageErrors.forEach(error => console.log('❌', error));
    }
    
    console.log('\n=== 控制台消息 ===');
    consoleMessages.forEach(msg => {
      if (msg.includes('error') || msg.includes('Error')) {
        console.log('❌', msg);
      } else if (msg.includes('warning') || msg.includes('Warning')) {
        console.log('⚠️', msg);
      } else {
        console.log('ℹ️', msg);
      }
    });
    
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    await browser.close();
  }
})();
