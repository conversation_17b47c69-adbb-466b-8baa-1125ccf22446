#!/usr/bin/env python3
"""
四指标同行布局测试脚本

验证当前价差率、时间窗口、MA周期、窗口进度四个指标同行显示且不跳动的效果
"""

import requests
import time
import logging
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FourIndicatorsLayoutTester:
    """四指标布局测试器"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        
    def check_server_status(self):
        """检查服务器状态"""
        try:
            response = requests.get(self.base_url, timeout=5)
            if response.status_code == 200:
                logger.info("✅ Web服务器运行正常")
                return True
            else:
                logger.error(f"❌ Web服务器响应异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 无法连接到Web服务器: {e}")
            return False
    
    def get_dashboard_html(self):
        """获取仪表板HTML内容"""
        try:
            response = requests.get(self.base_url, timeout=10)
            if response.status_code == 200:
                return response.text
            else:
                logger.error(f"❌ 获取仪表板失败: {response.status_code}")
                return None
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 获取仪表板异常: {e}")
            return None
    
    def validate_four_indicators_layout(self, html_content):
        """验证四指标布局"""
        logger.info("🔍 验证四指标布局...")
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找包含四个指标的容器
        container = soup.find('div', class_='d-flex align-items-center flex-wrap')
        if not container:
            # 尝试查找其他可能的容器
            containers = soup.find_all('div', class_='d-flex')
            logger.info(f"找到 {len(containers)} 个d-flex容器")

            # 直接查找价差率元素并获取其父容器
            spread_rate_element = soup.find('span', {'id': 'currentSpreadRate'})
            if spread_rate_element:
                logger.info("✅ 找到价差率元素")
                container = spread_rate_element.parent.parent  # 获取父容器
                logger.info(f"父容器类: {container.get('class', [])}")
                logger.info(f"父容器样式: {container.get('style', '')}")
            else:
                logger.error("❌ 未找到价差率元素")
                return False
        
        logger.info("✅ 找到flex-wrap容器")
        
        # 检查是否包含四个主要元素
        spread_rate_element = container.find('span', {'id': 'currentSpreadRate'})
        time_window_element = container.find('span', {'id': 'timeWindowDisplay'})
        ma_period_element = container.find('span', {'id': 'maPeriodDisplay'})
        window_progress_element = container.find('span', {'id': 'windowProgress'})
        
        indicators = {
            'currentSpreadRate': spread_rate_element,
            'timeWindowDisplay': time_window_element,
            'maPeriodDisplay': ma_period_element,
            'windowProgress': window_progress_element
        }
        
        missing_indicators = [name for name, element in indicators.items() if element is None]
        if missing_indicators:
            logger.error(f"❌ 缺少指标元素: {missing_indicators}")
            return False
        
        logger.info("✅ 找到所有四个指标元素")
        
        # 检查固定宽度样式
        for name, element in indicators.items():
            style = element.get('style', '')
            if 'min-width' not in style:
                logger.error(f"❌ {name}元素缺少固定宽度")
                return False
        
        logger.info("✅ 所有元素都有固定宽度设置")
        
        # 检查gap间距设置
        container_style = container.get('style', '')
        if 'gap:' not in container_style:
            logger.error("❌ 容器缺少gap间距设置")
            return False
        
        logger.info("✅ 容器使用gap间距")
        
        # 检查等宽字体设置
        if 'monospace' not in container_style:
            logger.error("❌ 容器缺少等宽字体设置")
            return False
        
        logger.info("✅ 容器使用等宽字体")
        
        return True
    
    def validate_responsive_design(self, html_content):
        """验证响应式设计"""
        logger.info("🔍 验证响应式设计...")

        soup = BeautifulSoup(html_content, 'html.parser')

        # 查找价差率元素并获取其父容器
        spread_rate_element = soup.find('span', {'id': 'currentSpreadRate'})
        if not spread_rate_element:
            logger.error("❌ 未找到价差率元素")
            return False

        container = spread_rate_element.parent.parent
        if not container:
            logger.error("❌ 未找到容器")
            return False
        
        # 检查flex-wrap类
        classes = container.get('class', [])
        if 'flex-wrap' not in classes:
            logger.error("❌ 缺少flex-wrap类")
            return False
        
        logger.info("✅ 使用flex-wrap实现响应式换行")
        
        return True
    
    def validate_anti_jumping_logic(self, html_content):
        """验证防跳动逻辑"""
        logger.info("🔍 验证防跳动逻辑...")
        
        # 检查JavaScript中的防跳动逻辑
        if 'currentMaPeriod !== maPeriod.toString()' in html_content:
            logger.info("✅ 找到MA周期防跳动逻辑")
        else:
            logger.error("❌ 未找到MA周期防跳动逻辑")
            return False
        
        if 'currentWindowDisplay !== expectedWindowDisplay' in html_content:
            logger.info("✅ 找到时间窗口防跳动逻辑")
        else:
            logger.error("❌ 未找到时间窗口防跳动逻辑")
            return False
        
        # 检查默认值设置
        if 'priceData.ma_period || 5' in html_content:
            logger.info("✅ 找到MA周期默认值设置")
        else:
            logger.error("❌ 未找到MA周期默认值设置")
            return False
        
        if 'priceData.window_minutes || 1' in html_content:
            logger.info("✅ 找到时间窗口默认值设置")
        else:
            logger.error("❌ 未找到时间窗口默认值设置")
            return False
        
        return True
    
    def simulate_layout_stability(self):
        """模拟布局稳定性测试"""
        logger.info("🔍 模拟布局稳定性测试...")
        
        # 模拟不同长度的指标值
        test_scenarios = [
            {
                'spread_rate': '+0.0001%',
                'time_window': '1分钟',
                'ma_period': '5',
                'window_progress': '0.0%'
            },
            {
                'spread_rate': '+0.0242%',
                'time_window': '1分钟',
                'ma_period': '5',
                'window_progress': '59.1%'
            },
            {
                'spread_rate': '-0.1234%',
                'time_window': '1分钟',
                'ma_period': '5',
                'window_progress': '100.0%'
            },
            {
                'spread_rate': '+12.3456%',
                'time_window': '1分钟',
                'ma_period': '5',
                'window_progress': '12.3%'
            }
        ]
        
        logger.info("📊 测试不同场景下的显示效果:")
        for i, scenario in enumerate(test_scenarios, 1):
            logger.info(f"  场景{i}: {scenario['spread_rate']:<10} {scenario['time_window']:<6} {scenario['ma_period']:<3} {scenario['window_progress']:<7}")
        
        # 分析固定宽度效果
        widths = {
            'currentSpreadRate': '80px',
            'timeWindowDisplay': '50px', 
            'maPeriodDisplay': '30px',
            'windowProgress': '60px'
        }
        
        logger.info("📏 固定宽度设置:")
        for element, width in widths.items():
            logger.info(f"  {element}: {width}")
        
        logger.info("✅ 使用固定宽度和gap间距，确保布局稳定")
        
        return True
    
    def run_layout_test(self):
        """运行完整的布局测试"""
        logger.info("=" * 60)
        logger.info("🔍 开始四指标同行布局测试")
        logger.info("=" * 60)
        
        # 1. 检查服务器状态
        if not self.check_server_status():
            logger.error("❌ 服务器不可用，无法进行测试")
            return False
        
        # 2. 获取HTML内容
        html_content = self.get_dashboard_html()
        if not html_content:
            logger.error("❌ 无法获取HTML内容")
            return False
        
        # 3. 验证四指标布局
        if not self.validate_four_indicators_layout(html_content):
            logger.error("❌ 四指标布局验证失败")
            return False
        
        # 4. 验证响应式设计
        if not self.validate_responsive_design(html_content):
            logger.error("❌ 响应式设计验证失败")
            return False
        
        # 5. 验证防跳动逻辑
        if not self.validate_anti_jumping_logic(html_content):
            logger.error("❌ 防跳动逻辑验证失败")
            return False
        
        # 6. 模拟布局稳定性测试
        if not self.simulate_layout_stability():
            logger.error("❌ 布局稳定性测试失败")
            return False
        
        logger.info("=" * 60)
        logger.info("✅ 四指标同行布局测试完成")
        logger.info("=" * 60)
        
        logger.info("📋 布局特性总结:")
        logger.info("  1. ✅ 四个指标在同一行显示")
        logger.info("  2. ✅ 使用flex-wrap实现响应式换行")
        logger.info("  3. ✅ 使用gap间距替代margin")
        logger.info("  4. ✅ 固定宽度避免跳动")
        logger.info("  5. ✅ 等宽字体确保对齐")
        logger.info("  6. ✅ JavaScript防跳动逻辑")
        
        logger.info("🎯 显示效果:")
        logger.info("  当前价差率: +0.0242%  时间窗口: 1分钟  MA周期: 5  窗口进度: 59.1%")
        logger.info("  当前价差率: -0.1234%  时间窗口: 1分钟  MA周期: 5  窗口进度: 100.0%")
        
        logger.info("💡 技术实现:")
        logger.info("  - d-flex align-items-center flex-wrap: 水平排列，支持换行")
        logger.info("  - gap: 15px: 统一的元素间距")
        logger.info("  - min-width + text-align: 固定宽度避免跳动")
        logger.info("  - font-family: monospace: 等宽字体确保对齐")
        logger.info("  - JavaScript条件更新: 只在值改变时更新DOM")
        
        logger.info("🔧 防跳动机制:")
        logger.info("  - MA周期: 只在值真正改变时更新")
        logger.info("  - 时间窗口: 只在值真正改变时更新")
        logger.info("  - 默认值: MA周期=5, 时间窗口=1分钟")
        
        return True

def main():
    """主函数"""
    tester = FourIndicatorsLayoutTester()
    
    try:
        success = tester.run_layout_test()
        
        if success:
            logger.info("🎉 四指标同行布局测试成功!")
            logger.info("💡 现在四个关键指标在同一行显示且不会跳动")
        else:
            logger.error("❌ 四指标同行布局测试失败!")
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
