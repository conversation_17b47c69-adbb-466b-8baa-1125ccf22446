# Binance-Lighter 套利交易系统 .gitignore
#
# 🔒 安全提醒：此文件确保敏感信息不会被意外提交到版本控制
# 📝 最后更新：2025-06-01
# 🎯 适用版本：v1.2.0+

# ============================================================================
# Python 相关文件
# ============================================================================

# 字节码文件
__pycache__/
*.py[cod]
*$py.class

# C 扩展
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
test-results/
test-reports/

# Translations
*.mo
*.pot

# Django (保留以防未来扩展)
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask (Web界面相关)
instance/
.webassets-cache

# Scrapy
.scrapy

# Sphinx 文档
docs/_build/
docs/build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# Poetry
poetry.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# 环境变量
.env
.env.*
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre
.pyre/

# Ruff
.ruff_cache/

# ============================================================================
# 套利交易系统特定文件
# ============================================================================

# 🗄️ 数据库文件
*.db
*.sqlite
*.sqlite3
data/*.db
data/*.sqlite
data/*.sqlite3
# 但保留数据库模式文件
!schema.sql
!init.sql

# 📦 数据备份文件
data/backups/
backups/
*.backup
*.bak
*_backup_*.db

# 🔒 进程锁文件
*.lock
data/*.lock
*.pid
*.pidfile

# 📝 日志文件
logs/
*.log
*.log.*
*.out
*.err

# 📊 临时数据文件
data/temp/
data/tmp/
data/cache/
data/realtime/
temp/
tmp/
cache/

# 📈 交易数据文件
data/historical/
data/market_data/
data/price_history/
data/trades/
data/orders/

# ============================================================================
# 🔐 敏感配置文件 (重要安全设置)
# ============================================================================

# ⚠️ 重要：包含API密钥的配置文件
config/exchanges.yaml

# 🔑 其他敏感配置
config/secrets.yaml
config/production.yaml
config/staging.yaml
config/*.secret
config/*.key
config/*.pem
config/*.crt
config/*.p12
config/*.pfx

# 🌍 环境特定配置
config/local.yaml
config/dev.yaml
config/test.yaml
.env.local
.env.production
.env.staging

# 🔐 密钥和证书文件
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer
*.der
private_key*
api_key*
secret_key*

# ============================================================================
# 💻 IDE 和编辑器文件
# ============================================================================

# VS Code
.vscode/
*.code-workspace
.vscode-test/

# PyCharm / IntelliJ
.idea/
*.iws
*.iml
*.ipr
out/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Nano
*.save

# Atom
.atom/

# Eclipse
.metadata
.project
.classpath
.settings/

# NetBeans
nbproject/
nbbuild/
nbdist/
.nb-gradle/

# ============================================================================
# 系统文件
# ============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ============================================================================
# 🧪 开发和测试文件
# ============================================================================

# 测试数据和输出
test_data/
test_logs/
test_output/
test_results/
test-reports/
test_*.db
test_*.log

# 性能分析
*.prof
*.cprof
*.pstats
profile_output/

# 调试文件
debug.log
debug_*.log
debug_*.json
*.debug

# Playwright测试
test-results/
playwright-report/
playwright/.cache/

# ============================================================================
# 🚀 部署和运维文件
# ============================================================================

# Docker
.dockerignore
docker-compose.override.yml
Dockerfile.local
.docker/

# Kubernetes
k8s-local/
*.kubeconfig

# 监控和日志
monitoring/
alerts/
metrics/

# 部署脚本和配置
deploy_secrets.sh
production_deploy.sh
deploy.sh
*.deploy

# CI/CD
.github/workflows/local.yml
.gitlab-ci-local.yml

# ============================================================================
# 📁 其他文件和目录
# ============================================================================

# 文档构建
docs/build/
docs/_build/
site/

# 缓存文件
.cache/
*.cache
.parcel-cache/

# 用户自定义配置
user_config.yaml
personal_settings.yaml
local_config.yaml

# 备注和临时文件
TODO.txt
NOTES.txt
*.notes
*.todo
scratch/
drafts/

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z
*.tar
*.gz

# 媒体文件
*.mp4
*.avi
*.mov
*.wmv

# ============================================================================
# 📊 交易系统专用忽略项
# ============================================================================

# 策略测试和回测结果
backtest_results/
strategy_reports/
optimization_results/
parameter_sweep/

# 错误转储和崩溃报告
core
*.dump
crash_reports/
error_dumps/

# 系统监控和性能数据
system_metrics/
performance_logs/
resource_usage/

# 机器学习模型文件
models/
*.model
*.pkl
*.joblib
*.h5

# 数据分析文件
analysis/
reports/
charts/
*.ipynb_checkpoints

# ============================================================================
# 📋 重要安全提醒和使用说明
# ============================================================================
#
# 🔐 安全规则：
# 1. API密钥和敏感信息永远不要提交到版本控制
# 2. 只提交配置模板文件（.template），不提交实际配置文件
# 3. 使用环境变量或外部密钥管理系统存储敏感信息
# 4. 定期审查此文件，确保新的敏感文件类型被正确忽略
#
# 📁 文件管理：
# 5. 数据库文件通常不应提交，除非是初始化脚本
# 6. 日志文件应该在运行时生成，不要提交到仓库
# 7. 个人配置文件应该被忽略，只提交模板文件
# 8. 测试数据和临时文件应该被忽略
#
# 🔄 维护建议：
# 9. 定期检查是否有新的文件类型需要忽略
# 10. 在添加新功能时，考虑是否需要更新.gitignore
# 11. 使用 `git status` 定期检查是否有意外的文件被跟踪
# 12. 团队成员应该了解并遵守这些忽略规则
#
# 🚨 紧急情况：
# 如果意外提交了敏感信息：
# 1. 立即撤销提交：git reset --hard HEAD~1
# 2. 强制推送：git push --force-with-lease
# 3. 更换所有相关的API密钥和密码
# 4. 检查Git历史，确保敏感信息完全清除
#
# 📞 联系信息：
# 如有疑问，请联系项目维护者或查看安全配置指南：
# docs/SECURITY_GUIDE.md
#
# 最后更新：2025-06-01
# 版本：v1.2.0