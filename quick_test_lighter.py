#!/usr/bin/env python3
"""
Lighter价格更新快速验证脚本

快速验证修复后的Lighter客户端连接稳定性
"""

import asyncio
import time
import logging
from datetime import datetime
from src.exchanges.lighter_client import LighterClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class QuickTester:
    """快速测试器"""
    
    def __init__(self):
        self.client = None
        self.update_count = 0
        self.start_time = None
        self.last_price = None
        
    def on_orderbook_update(self, orderbook):
        """订单簿更新回调"""
        self.update_count += 1
        
        if orderbook.bids and orderbook.asks:
            mid_price = (orderbook.bids[0][0] + orderbook.asks[0][0]) / 2
            
            # 每10次更新显示一次
            if self.update_count % 10 == 0:
                elapsed = time.time() - self.start_time
                rate = self.update_count / elapsed * 60  # 每分钟更新次数
                
                logger.info(f"更新#{self.update_count}: 价格={mid_price:.2f}, "
                           f"频率={rate:.1f}/分钟, "
                           f"连接质量={self.client.status.get('connection_quality', 0)}/100")
    
    async def quick_test(self, duration_seconds=60):
        """快速测试"""
        logger.info(f"🧪 开始Lighter快速测试 (持续{duration_seconds}秒)")
        
        self.start_time = time.time()
        
        # 初始化客户端
        self.client = LighterClient(symbol="BTC/USDT", is_paper_trading=True)
        self.client.set_orderbook_callback(self.on_orderbook_update)
        
        # 初始化连接
        success = await self.client.initialize()
        if not success:
            logger.error("❌ 客户端初始化失败")
            return False
        
        logger.info("✅ 客户端初始化成功，开始监控...")
        
        # 监控指定时间
        await asyncio.sleep(duration_seconds)
        
        # 生成报告
        elapsed = time.time() - self.start_time
        avg_rate = self.update_count / elapsed * 60
        
        status = self.client.status
        
        logger.info("=" * 50)
        logger.info("📊 快速测试结果")
        logger.info("=" * 50)
        logger.info(f"测试时长: {elapsed:.1f} 秒")
        logger.info(f"总更新次数: {self.update_count}")
        logger.info(f"平均更新频率: {avg_rate:.1f} 次/分钟")
        logger.info(f"连接状态: {status.get('connection_status', 'unknown')}")
        logger.info(f"连接健康: {status.get('connection_health', 'unknown')}")
        logger.info(f"连接质量: {status.get('connection_quality', 0)}/100")
        logger.info(f"重连次数: {status.get('reconnect_attempts', 0)}")
        
        # 评估结果
        if avg_rate > 60 and status.get('connection_health') == 'healthy':
            logger.info("✅ 测试通过: 连接稳定，价格更新正常")
            result = True
        elif avg_rate > 30:
            logger.info("⚠️ 测试部分通过: 连接基本稳定")
            result = True
        else:
            logger.info("❌ 测试失败: 连接不稳定或更新频率过低")
            result = False
        
        logger.info("=" * 50)
        
        # 清理
        await self.client.close()
        return result

async def main():
    """主函数"""
    tester = QuickTester()
    
    try:
        # 运行1分钟的快速测试
        success = await tester.quick_test(duration_seconds=60)
        
        if success:
            logger.info("🎉 Lighter修复验证成功！")
        else:
            logger.info("⚠️ Lighter修复需要进一步优化")
            
    except KeyboardInterrupt:
        logger.info("🛑 测试被用户中断")
        if tester.client:
            await tester.client.close()
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        if tester.client:
            await tester.client.close()

if __name__ == "__main__":
    asyncio.run(main())
